import{u as v0,j as nt,T as N1,b as tc,S as L0,c as lc,P as ic,d as q1,G as jd,B as H0,C as N0,e as q0,M as G1}from"./_better_markdown_anki_chunk_ui-libs-D95AHZAA.js";import{b as Y1,d as X1,r as Ft,c as Yd}from"./_better_markdown_anki_chunk_react-vendor-Bkk_KtsK.js";import{c as G0,v as Q1,o as rc,u as ac,a as b0,n as hs,l as Z1,b as Qd,d as V1,t as Y0,E as K1,e as y0,f as os,m as il,g as j1,h as J1,i as Pu,s as a0,r as W1,j as fs,k as ec,p as F1,M as $1,q as I1,w as P1,x as tm,y as em,z as nm}from"./_better_markdown_anki_chunk_content-libs-BQ7EujUu.js";import{I as X0}from"./_better_markdown_anki_chunk_visual-libs-z8icT73K.js";(function(){const m=document.createElement("link").relList;if(m&&m.supports&&m.supports("modulepreload"))return;for(const S of document.querySelectorAll('link[rel="modulepreload"]'))g(S);new MutationObserver(S=>{for(const T of S)if(T.type==="childList")for(const y of T.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&g(y)}).observe(document,{childList:!0,subtree:!0});function c(S){const T={};return S.integrity&&(T.integrity=S.integrity),S.referrerPolicy&&(T.referrerPolicy=S.referrerPolicy),S.crossOrigin==="use-credentials"?T.credentials="include":S.crossOrigin==="anonymous"?T.credentials="omit":T.credentials="same-origin",T}function g(S){if(S.ep)return;S.ep=!0;const T=c(S);fetch(S.href,T)}})();var r0={exports:{}},rs={},f0={exports:{}},c0={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var T0;function lm(){return T0||(T0=1,function(o){function m(X,$){var ft=X.length;X.push($);t:for(;0<ft;){var It=ft-1>>>1,bt=X[It];if(0<S(bt,$))X[It]=$,X[ft]=bt,ft=It;else break t}}function c(X){return X.length===0?null:X[0]}function g(X){if(X.length===0)return null;var $=X[0],ft=X.pop();if(ft!==$){X[0]=ft;t:for(var It=0,bt=X.length,ae=bt>>>1;It<ae;){var fe=2*(It+1)-1,Zt=X[fe],Te=fe+1,Ue=X[Te];if(0>S(Zt,ft))Te<bt&&0>S(Ue,Zt)?(X[It]=Ue,X[Te]=ft,It=Te):(X[It]=Zt,X[fe]=ft,It=fe);else if(Te<bt&&0>S(Ue,ft))X[It]=Ue,X[Te]=ft,It=Te;else break t}}return $}function S(X,$){var ft=X.sortIndex-$.sortIndex;return ft!==0?ft:X.id-$.id}if(o.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var T=performance;o.unstable_now=function(){return T.now()}}else{var y=Date,z=y.now();o.unstable_now=function(){return y.now()-z}}var x=[],w=[],q=1,M=null,N=3,F=!1,Z=!1,I=!1,et=!1,ut=typeof setTimeout=="function"?setTimeout:null,dt=typeof clearTimeout=="function"?clearTimeout:null,it=typeof setImmediate<"u"?setImmediate:null;function Ut(X){for(var $=c(w);$!==null;){if($.callback===null)g(w);else if($.startTime<=X)g(w),$.sortIndex=$.expirationTime,m(x,$);else break;$=c(w)}}function Ct(X){if(I=!1,Ut(X),!Z)if(c(x)!==null)Z=!0,Lt||(Lt=!0,Et());else{var $=c(w);$!==null&&ul(Ct,$.startTime-X)}}var Lt=!1,$t=-1,St=5,ue=-1;function K(){return et?!0:!(o.unstable_now()-ue<St)}function Re(){if(et=!1,Lt){var X=o.unstable_now();ue=X;var $=!0;try{t:{Z=!1,I&&(I=!1,dt($t),$t=-1),F=!0;var ft=N;try{e:{for(Ut(X),M=c(x);M!==null&&!(M.expirationTime>X&&K());){var It=M.callback;if(typeof It=="function"){M.callback=null,N=M.priorityLevel;var bt=It(M.expirationTime<=X);if(X=o.unstable_now(),typeof bt=="function"){M.callback=bt,Ut(X),$=!0;break e}M===c(x)&&g(x),Ut(X)}else g(x);M=c(x)}if(M!==null)$=!0;else{var ae=c(w);ae!==null&&ul(Ct,ae.startTime-X),$=!1}}break t}finally{M=null,N=ft,F=!1}$=void 0}}finally{$?Et():Lt=!1}}}var Et;if(typeof it=="function")Et=function(){it(Re)};else if(typeof MessageChannel<"u"){var Qt=new MessageChannel,en=Qt.port2;Qt.port1.onmessage=Re,Et=function(){en.postMessage(null)}}else Et=function(){ut(Re,0)};function ul(X,$){$t=ut(function(){X(o.unstable_now())},$)}o.unstable_IdlePriority=5,o.unstable_ImmediatePriority=1,o.unstable_LowPriority=4,o.unstable_NormalPriority=3,o.unstable_Profiling=null,o.unstable_UserBlockingPriority=2,o.unstable_cancelCallback=function(X){X.callback=null},o.unstable_forceFrameRate=function(X){0>X||125<X?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):St=0<X?Math.floor(1e3/X):5},o.unstable_getCurrentPriorityLevel=function(){return N},o.unstable_next=function(X){switch(N){case 1:case 2:case 3:var $=3;break;default:$=N}var ft=N;N=$;try{return X()}finally{N=ft}},o.unstable_requestPaint=function(){et=!0},o.unstable_runWithPriority=function(X,$){switch(X){case 1:case 2:case 3:case 4:case 5:break;default:X=3}var ft=N;N=X;try{return $()}finally{N=ft}},o.unstable_scheduleCallback=function(X,$,ft){var It=o.unstable_now();switch(typeof ft=="object"&&ft!==null?(ft=ft.delay,ft=typeof ft=="number"&&0<ft?It+ft:It):ft=It,X){case 1:var bt=-1;break;case 2:bt=250;break;case 5:bt=1073741823;break;case 4:bt=1e4;break;default:bt=5e3}return bt=ft+bt,X={id:q++,callback:$,priorityLevel:X,startTime:ft,expirationTime:bt,sortIndex:-1},ft>It?(X.sortIndex=ft,m(w,X),c(x)===null&&X===c(w)&&(I?(dt($t),$t=-1):I=!0,ul(Ct,ft-It))):(X.sortIndex=bt,m(x,X),Z||F||(Z=!0,Lt||(Lt=!0,Et()))),X},o.unstable_shouldYield=K,o.unstable_wrapCallback=function(X){var $=N;return function(){var ft=N;N=$;try{return X.apply(this,arguments)}finally{N=ft}}}}(c0)),c0}var w0;function im(){return w0||(w0=1,f0.exports=lm()),f0.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k0;function um(){if(k0)return rs;k0=1;var o=im(),m=Y1(),c=X1();function g(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function S(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function T(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function y(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function z(t){if(T(t)!==t)throw Error(g(188))}function x(t){var e=t.alternate;if(!e){if(e=T(t),e===null)throw Error(g(188));return e!==t?null:t}for(var n=t,u=e;;){var a=n.return;if(a===null)break;var f=a.alternate;if(f===null){if(u=a.return,u!==null){n=u;continue}break}if(a.child===f.child){for(f=a.child;f;){if(f===n)return z(a),t;if(f===u)return z(a),e;f=f.sibling}throw Error(g(188))}if(n.return!==u.return)n=a,u=f;else{for(var h=!1,v=a.child;v;){if(v===n){h=!0,n=a,u=f;break}if(v===u){h=!0,u=a,n=f;break}v=v.sibling}if(!h){for(v=f.child;v;){if(v===n){h=!0,n=f,u=a;break}if(v===u){h=!0,u=f,n=a;break}v=v.sibling}if(!h)throw Error(g(189))}}if(n.alternate!==u)throw Error(g(190))}if(n.tag!==3)throw Error(g(188));return n.stateNode.current===n?t:e}function w(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=w(t),e!==null)return e;t=t.sibling}return null}var q=Object.assign,M=Symbol.for("react.element"),N=Symbol.for("react.transitional.element"),F=Symbol.for("react.portal"),Z=Symbol.for("react.fragment"),I=Symbol.for("react.strict_mode"),et=Symbol.for("react.profiler"),ut=Symbol.for("react.provider"),dt=Symbol.for("react.consumer"),it=Symbol.for("react.context"),Ut=Symbol.for("react.forward_ref"),Ct=Symbol.for("react.suspense"),Lt=Symbol.for("react.suspense_list"),$t=Symbol.for("react.memo"),St=Symbol.for("react.lazy"),ue=Symbol.for("react.activity"),K=Symbol.for("react.memo_cache_sentinel"),Re=Symbol.iterator;function Et(t){return t===null||typeof t!="object"?null:(t=Re&&t[Re]||t["@@iterator"],typeof t=="function"?t:null)}var Qt=Symbol.for("react.client.reference");function en(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Qt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Z:return"Fragment";case et:return"Profiler";case I:return"StrictMode";case Ct:return"Suspense";case Lt:return"SuspenseList";case ue:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case F:return"Portal";case it:return(t.displayName||"Context")+".Provider";case dt:return(t._context.displayName||"Context")+".Consumer";case Ut:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case $t:return e=t.displayName||null,e!==null?e:en(t.type)||"Memo";case St:e=t._payload,t=t._init;try{return en(t(e))}catch{}}return null}var ul=Array.isArray,X=m.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ft={pending:!1,data:null,method:null,action:null},It=[],bt=-1;function ae(t){return{current:t}}function fe(t){0>bt||(t.current=It[bt],It[bt]=null,bt--)}function Zt(t,e){bt++,It[bt]=t.current,t.current=e}var Te=ae(null),Ue=ae(null),we=ae(null),br=ae(null);function Gl(t,e){switch(Zt(we,e),Zt(Ue,t),Zt(Te,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?me(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=me(e),t=pe(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}fe(Te),Zt(Te,t)}function al(){fe(Te),fe(Ue),fe(we)}function fc(t){t.memoizedState!==null&&Zt(br,t);var e=Te.current,n=pe(e,t.type);e!==n&&(Zt(Ue,t),Zt(Te,n))}function Si(t){Ue.current===t&&(fe(Te),fe(Ue)),br.current===t&&(fe(br),$u._currentValue=ft)}var Fi=Object.prototype.hasOwnProperty,yr=o.unstable_scheduleCallback,bn=o.unstable_cancelCallback,ta=o.unstable_shouldYield,Jd=o.unstable_requestPaint,Be=o.unstable_now,ds=o.unstable_getCurrentPriorityLevel,gs=o.unstable_ImmediatePriority,$i=o.unstable_UserBlockingPriority,nn=o.unstable_NormalPriority,ea=o.unstable_LowPriority,na=o.unstable_IdlePriority,Wd=o.log,la=o.unstable_setDisableYieldValue,ia=null,Xe=null;function yn(t){if(typeof Wd=="function"&&la(t),Xe&&typeof Xe.setStrictMode=="function")try{Xe.setStrictMode(ia,t)}catch{}}var Je=Math.clz32?Math.clz32:sc,cc=Math.log,oc=Math.LN2;function sc(t){return t>>>=0,t===0?32:31-(cc(t)/oc|0)|0}var Ii=256,Pi=4194304;function Sl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function tu(t,e,n){var u=t.pendingLanes;if(u===0)return 0;var a=0,f=t.suspendedLanes,h=t.pingedLanes;t=t.warmLanes;var v=u&134217727;return v!==0?(u=v&~f,u!==0?a=Sl(u):(h&=v,h!==0?a=Sl(h):n||(n=v&~t,n!==0&&(a=Sl(n))))):(v=u&~f,v!==0?a=Sl(v):h!==0?a=Sl(h):n||(n=u&~t,n!==0&&(a=Sl(n)))),a===0?0:e!==0&&e!==a&&(e&f)===0&&(f=a&-a,n=e&-e,f>=n||f===32&&(n&4194048)!==0)?e:a}function _i(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Fd(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ms(){var t=Ii;return Ii<<=1,(Ii&4194048)===0&&(Ii=256),t}function ps(){var t=Pi;return Pi<<=1,(Pi&62914560)===0&&(Pi=4194304),t}function Sr(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function eu(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function $d(t,e,n,u,a,f){var h=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var v=t.entanglements,A=t.expirationTimes,U=t.hiddenUpdates;for(n=h&~n;0<n;){var Y=31-Je(n),j=1<<Y;v[Y]=0,A[Y]=-1;var L=U[Y];if(L!==null)for(U[Y]=null,Y=0;Y<L.length;Y++){var B=L[Y];B!==null&&(B.lane&=-536870913)}n&=~j}u!==0&&vs(t,u,0),f!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=f&~(h&~e))}function vs(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var u=31-Je(e);t.entangledLanes|=e,t.entanglements[u]=t.entanglements[u]|1073741824|n&4194090}function bs(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var u=31-Je(n),a=1<<u;a&e|t[u]&e&&(t[u]|=e),n&=~a}}function hc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function _r(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function ys(){var t=$.p;return t!==0?t:(t=window.event,t===void 0?32:Bd(t.type))}function Id(t,e){var n=$.p;try{return $.p=t,e()}finally{$.p=n}}var Yl=Math.random().toString(36).slice(2),Le="__reactFiber$"+Yl,ln="__reactProps$"+Yl,Xl="__reactContainer$"+Yl,dc="__reactEvents$"+Yl,Pd="__reactListeners$"+Yl,tg="__reactHandles$"+Yl,Ss="__reactResources$"+Yl,ua="__reactMarker$"+Yl;function gc(t){delete t[Le],delete t[ln],delete t[dc],delete t[Pd],delete t[tg]}function nu(t){var e=t[Le];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Xl]||n[Le]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Ad(t);t!==null;){if(n=t[Le])return n;t=Ad(t)}return e}t=n,n=t.parentNode}return null}function lu(t){if(t=t[Le]||t[Xl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function iu(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(g(33))}function uu(t){var e=t[Ss];return e||(e=t[Ss]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function He(t){t[ua]=!0}var _s=new Set,xs={};function xi(t,e){au(t,e),au(t+"Capture",e)}function au(t,e){for(xs[t]=e,t=0;t<e.length;t++)_s.add(e[t])}var xr=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),As={},aa={};function eg(t){return Fi.call(aa,t)?!0:Fi.call(As,t)?!1:xr.test(t)?aa[t]=!0:(As[t]=!0,!1)}function Ar(t,e,n){if(eg(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var u=e.toLowerCase().slice(0,5);if(u!=="data-"&&u!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Tr(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function rl(t,e,n,u){if(u===null)t.removeAttribute(n);else{switch(typeof u){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+u)}}var wr,mc;function ru(t){if(wr===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);wr=e&&e[1]||"",mc=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+wr+t+mc}var pc=!1;function vc(t,e){if(!t||pc)return"";pc=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var u={DetermineComponentFrameRoot:function(){try{if(e){var j=function(){throw Error()};if(Object.defineProperty(j.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(j,[])}catch(B){var L=B}Reflect.construct(t,[],j)}else{try{j.call()}catch(B){L=B}t.call(j.prototype)}}else{try{throw Error()}catch(B){L=B}(j=t())&&typeof j.catch=="function"&&j.catch(function(){})}}catch(B){if(B&&L&&typeof B.stack=="string")return[B.stack,L.stack]}return[null,null]}};u.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(u.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(u.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=u.DetermineComponentFrameRoot(),h=f[0],v=f[1];if(h&&v){var A=h.split(`
`),U=v.split(`
`);for(a=u=0;u<A.length&&!A[u].includes("DetermineComponentFrameRoot");)u++;for(;a<U.length&&!U[a].includes("DetermineComponentFrameRoot");)a++;if(u===A.length||a===U.length)for(u=A.length-1,a=U.length-1;1<=u&&0<=a&&A[u]!==U[a];)a--;for(;1<=u&&0<=a;u--,a--)if(A[u]!==U[a]){if(u!==1||a!==1)do if(u--,a--,0>a||A[u]!==U[a]){var Y=`
`+A[u].replace(" at new "," at ");return t.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",t.displayName)),Y}while(1<=u&&0<=a);break}}}finally{pc=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ru(n):""}function ng(t){switch(t.tag){case 26:case 27:case 5:return ru(t.type);case 16:return ru("Lazy");case 13:return ru("Suspense");case 19:return ru("SuspenseList");case 0:case 15:return vc(t.type,!1);case 11:return vc(t.type.render,!1);case 1:return vc(t.type,!0);case 31:return ru("Activity");default:return""}}function bc(t){try{var e="";do e+=ng(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Sn(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function yc(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Sc(t){var e=yc(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),u=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,f=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(h){u=""+h,f.call(this,h)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return u},setValue:function(h){u=""+h},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function kr(t){t._valueTracker||(t._valueTracker=Sc(t))}function _c(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),u="";return t&&(u=yc(t)?t.checked?"true":"false":t.value),t=u,t!==n?(e.setValue(t),!0):!1}function Ai(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Ts=/[\n"\\]/g;function On(t){return t.replace(Ts,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function zr(t,e,n,u,a,f,h,v){t.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.type=h:t.removeAttribute("type"),e!=null?h==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Sn(e)):t.value!==""+Sn(e)&&(t.value=""+Sn(e)):h!=="submit"&&h!=="reset"||t.removeAttribute("value"),e!=null?ra(t,h,Sn(e)):n!=null?ra(t,h,Sn(n)):u!=null&&t.removeAttribute("value"),a==null&&f!=null&&(t.defaultChecked=!!f),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?t.name=""+Sn(v):t.removeAttribute("name")}function xc(t,e,n,u,a,f,h,v){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||n!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;n=n!=null?""+Sn(n):"",e=e!=null?""+Sn(e):n,v||e===t.value||(t.value=e),t.defaultValue=e}u=u??a,u=typeof u!="function"&&typeof u!="symbol"&&!!u,t.checked=v?t.checked:!!u,t.defaultChecked=!!u,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(t.name=h)}function ra(t,e,n){e==="number"&&Ai(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function fu(t,e,n,u){if(t=t.options,e){e={};for(var a=0;a<n.length;a++)e["$"+n[a]]=!0;for(n=0;n<t.length;n++)a=e.hasOwnProperty("$"+t[n].value),t[n].selected!==a&&(t[n].selected=a),a&&u&&(t[n].defaultSelected=!0)}else{for(n=""+Sn(n),e=null,a=0;a<t.length;a++){if(t[a].value===n){t[a].selected=!0,u&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function Ac(t,e,n){if(e!=null&&(e=""+Sn(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Sn(n):""}function Er(t,e,n,u){if(e==null){if(u!=null){if(n!=null)throw Error(g(92));if(ul(u)){if(1<u.length)throw Error(g(93));u=u[0]}n=u}n==null&&(n=""),e=n}n=Sn(e),t.defaultValue=n,u=t.textContent,u===n&&u!==""&&u!==null&&(t.value=u)}function Ql(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var cu=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Tc(t,e,n){var u=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?u?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":u?t.setProperty(e,n):typeof n!="number"||n===0||cu.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function wc(t,e,n){if(e!=null&&typeof e!="object")throw Error(g(62));if(t=t.style,n!=null){for(var u in n)!n.hasOwnProperty(u)||e!=null&&e.hasOwnProperty(u)||(u.indexOf("--")===0?t.setProperty(u,""):u==="float"?t.cssFloat="":t[u]="");for(var a in e)u=e[a],e.hasOwnProperty(a)&&n[a]!==u&&Tc(t,a,u)}else for(var f in e)e.hasOwnProperty(f)&&Tc(t,f,e[f])}function kc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ws=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ks=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function fa(t){return ks.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Mr=null;function zc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ou=null,su=null;function Ec(t){var e=lu(t);if(e&&(t=e.stateNode)){var n=t[ln]||null;t:switch(t=e.stateNode,e.type){case"input":if(zr(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+On(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var u=n[e];if(u!==t&&u.form===t.form){var a=u[ln]||null;if(!a)throw Error(g(90));zr(u,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<n.length;e++)u=n[e],u.form===t.form&&_c(u)}break t;case"textarea":Ac(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&fu(t,!!n.multiple,e,!1)}}}var Mc=!1;function zs(t,e,n){if(Mc)return t(e,n);Mc=!0;try{var u=t(e);return u}finally{if(Mc=!1,(ou!==null||su!==null)&&(Df(),ou&&(e=ou,t=su,su=ou=null,Ec(e),t)))for(e=0;e<t.length;e++)Ec(t[e])}}function ca(t,e){var n=t.stateNode;if(n===null)return null;var u=n[ln]||null;if(u===null)return null;n=u[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(t=t.type,u=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!u;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(g(231,e,typeof n));return n}var _l=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),oa=!1;if(_l)try{var sa={};Object.defineProperty(sa,"passive",{get:function(){oa=!0}}),window.addEventListener("test",sa,sa),window.removeEventListener("test",sa,sa)}catch{oa=!1}var Zl=null,Dc=null,Dr=null;function Es(){if(Dr)return Dr;var t,e=Dc,n=e.length,u,a="value"in Zl?Zl.value:Zl.textContent,f=a.length;for(t=0;t<n&&e[t]===a[t];t++);var h=n-t;for(u=1;u<=h&&e[n-u]===a[f-u];u++);return Dr=a.slice(t,1<u?1-u:void 0)}function Pt(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Kt(){return!0}function Ms(){return!1}function un(t){function e(n,u,a,f,h){this._reactName=n,this._targetInst=a,this.type=u,this.nativeEvent=f,this.target=h,this.currentTarget=null;for(var v in t)t.hasOwnProperty(v)&&(n=t[v],this[v]=n?n(f):f[v]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Kt:Ms,this.isPropagationStopped=Ms,this}return q(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Kt)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Kt)},persist:function(){},isPersistent:Kt}),e}var Ti={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Cr=un(Ti),ha=q({},Ti,{view:0,detail:0}),lg=un(ha),Or,Cc,_e,hu=q({},ha,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fl,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==_e&&(_e&&t.type==="mousemove"?(Or=t.screenX-_e.screenX,Cc=t.screenY-_e.screenY):Cc=Or=0,_e=t),Or)},movementY:function(t){return"movementY"in t?t.movementY:Cc}}),Vl=un(hu),Ds=q({},hu,{dataTransfer:0}),Oc=un(Ds),Rn=q({},ha,{relatedTarget:0}),Rr=un(Rn),Cs=q({},Ti,{animationName:0,elapsedTime:0,pseudoElement:0}),Os=un(Cs),Rs=q({},Ti,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Us=un(Rs),Bs=q({},Ti,{data:0}),an=un(Bs),ig={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Un={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ug={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ls(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=ug[t])?!!e[t]:!1}function fl(){return Ls}var Ur=q({},ha,{key:function(t){if(t.key){var e=ig[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Pt(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Un[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fl,charCode:function(t){return t.type==="keypress"?Pt(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Pt(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Rc=un(Ur),se=q({},hu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),xl=un(se),Uc=q({},ha,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fl}),ag=un(Uc),Bc=q({},Ti,{propertyName:0,elapsedTime:0,pseudoElement:0}),rg=un(Bc),fg=q({},hu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),cg=un(fg),Hs=q({},Ti,{newState:0,oldState:0}),Br=un(Hs),du=[9,13,27,32],Lc=_l&&"CompositionEvent"in window,gu=null;_l&&"documentMode"in document&&(gu=document.documentMode);var Ns=_l&&"TextEvent"in window&&!gu,Lr=_l&&(!Lc||gu&&8<gu&&11>=gu),Hr=" ",Hc=!1;function qs(t,e){switch(t){case"keyup":return du.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nr(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Kl=!1;function og(t,e){switch(t){case"compositionend":return Nr(e);case"keypress":return e.which!==32?null:(Hc=!0,Hr);case"textInput":return t=e.data,t===Hr&&Hc?null:t;default:return null}}function Gs(t,e){if(Kl)return t==="compositionend"||!Lc&&qs(t,e)?(t=Es(),Dr=Dc=Zl=null,Kl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Lr&&e.locale!=="ko"?null:e.data;default:return null}}var _n={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qr(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!_n[t.type]:e==="textarea"}function mu(t,e,n,u){ou?su?su.push(u):su=[u]:ou=u,e=Hf(e,"onChange"),0<e.length&&(n=new Cr("onChange","change",null,n,u),t.push({event:n,listeners:e}))}var pu=null,vu=null;function sg(t){md(t,0)}function Gr(t){var e=iu(t);if(_c(e))return t}function Ys(t,e){if(t==="change")return e}var Xs=!1;if(_l){var Nc;if(_l){var jl="oninput"in document;if(!jl){var Qs=document.createElement("div");Qs.setAttribute("oninput","return;"),jl=typeof Qs.oninput=="function"}Nc=jl}else Nc=!1;Xs=Nc&&(!document.documentMode||9<document.documentMode)}function Zs(){pu&&(pu.detachEvent("onpropertychange",Yr),vu=pu=null)}function Yr(t){if(t.propertyName==="value"&&Gr(vu)){var e=[];mu(e,vu,t,zc(t)),zs(sg,e)}}function Vs(t,e,n){t==="focusin"?(Zs(),pu=e,vu=n,pu.attachEvent("onpropertychange",Yr)):t==="focusout"&&Zs()}function Jl(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Gr(vu)}function Xr(t,e){if(t==="click")return Gr(e)}function hg(t,e){if(t==="input"||t==="change")return Gr(e)}function dg(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var xn=typeof Object.is=="function"?Object.is:dg;function cl(t,e){if(xn(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),u=Object.keys(e);if(n.length!==u.length)return!1;for(u=0;u<n.length;u++){var a=n[u];if(!Fi.call(e,a)||!xn(t[a],e[a]))return!1}return!0}function Bn(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function qc(t,e){var n=Bn(t);t=0;for(var u;n;){if(n.nodeType===3){if(u=t+n.textContent.length,t<=e&&u>=e)return{node:n,offset:e-t};t=u}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Bn(n)}}function Ks(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ks(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function js(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ai(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Ai(t.document)}return e}function Gc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var gg=_l&&"documentMode"in document&&11>=document.documentMode,bu=null,Wl=null,D=null,G=!1;function H(t,e,n){var u=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;G||bu==null||bu!==Ai(u)||(u=bu,"selectionStart"in u&&Gc(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),D&&cl(D,u)||(D=u,u=Hf(Wl,"onSelect"),0<u.length&&(e=new Cr("onSelect","select",null,e,n),t.push({event:e,listeners:u}),e.target=bu)))}function P(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var gt={animationend:P("Animation","AnimationEnd"),animationiteration:P("Animation","AnimationIteration"),animationstart:P("Animation","AnimationStart"),transitionrun:P("Transition","TransitionRun"),transitionstart:P("Transition","TransitionStart"),transitioncancel:P("Transition","TransitionCancel"),transitionend:P("Transition","TransitionEnd")},Ht={},xe={};_l&&(xe=document.createElement("div").style,"AnimationEvent"in window||(delete gt.animationend.animation,delete gt.animationiteration.animation,delete gt.animationstart.animation),"TransitionEvent"in window||delete gt.transitionend.transition);function Nt(t){if(Ht[t])return Ht[t];if(!gt[t])return t;var e=gt[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in xe)return Ht[t]=e[n];return t}var Qr=Nt("animationend"),Js=Nt("animationiteration"),An=Nt("animationstart"),Zr=Nt("transitionrun"),mg=Nt("transitionstart"),yu=Nt("transitioncancel"),da=Nt("transitionend"),ga=new Map,Yt="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Yt.push("scrollEnd");function jn(t,e){ga.set(t,e),xi(e,[t])}var Yc=new WeakMap;function We(t,e){if(typeof t=="object"&&t!==null){var n=Yc.get(t);return n!==void 0?n:(e={value:t,source:e,stack:bc(e)},Yc.set(t,e),e)}return{value:t,source:e,stack:bc(e)}}var Ln=[],Su=0,Xc=0;function wi(){for(var t=Su,e=Xc=Su=0;e<t;){var n=Ln[e];Ln[e++]=null;var u=Ln[e];Ln[e++]=null;var a=Ln[e];Ln[e++]=null;var f=Ln[e];if(Ln[e++]=null,u!==null&&a!==null){var h=u.pending;h===null?a.next=a:(a.next=h.next,h.next=a),u.pending=a}f!==0&&ma(n,a,f)}}function Jn(t,e,n,u){Ln[Su++]=t,Ln[Su++]=e,Ln[Su++]=n,Ln[Su++]=u,Xc|=u,t.lanes|=u,t=t.alternate,t!==null&&(t.lanes|=u)}function _u(t,e,n,u){return Jn(t,e,n,u),pa(t)}function ki(t,e){return Jn(t,null,null,e),pa(t)}function ma(t,e,n){t.lanes|=n;var u=t.alternate;u!==null&&(u.lanes|=n);for(var a=!1,f=t.return;f!==null;)f.childLanes|=n,u=f.alternate,u!==null&&(u.childLanes|=n),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(a=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,a&&e!==null&&(a=31-Je(n),t=f.hiddenUpdates,u=t[a],u===null?t[a]=[e]:u.push(e),e.lane=n|536870912),f):null}function pa(t){if(50<Qu)throw Qu=0,Ho=null,Error(g(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var zi={};function Vr(t,e,n,u){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function rn(t,e,n,u){return new Vr(t,e,n,u)}function Ei(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Fe(t,e){var n=t.alternate;return n===null?(n=rn(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function va(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Kr(t,e,n,u,a,f){var h=0;if(u=t,typeof t=="function")Ei(t)&&(h=1);else if(typeof t=="string")h=Kg(t,n,Te.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case ue:return t=rn(31,n,e,a),t.elementType=ue,t.lanes=f,t;case Z:return Mi(n.children,a,f,e);case I:h=8,a|=24;break;case et:return t=rn(12,n,e,a|2),t.elementType=et,t.lanes=f,t;case Ct:return t=rn(13,n,e,a),t.elementType=Ct,t.lanes=f,t;case Lt:return t=rn(19,n,e,a),t.elementType=Lt,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ut:case it:h=10;break t;case dt:h=9;break t;case Ut:h=11;break t;case $t:h=14;break t;case St:h=16,u=null;break t}h=29,n=Error(g(130,t===null?"null":typeof t,"")),u=null}return e=rn(h,n,e,a),e.elementType=t,e.type=u,e.lanes=f,e}function Mi(t,e,n,u){return t=rn(7,t,u,e),t.lanes=n,t}function Qc(t,e,n){return t=rn(6,t,null,e),t.lanes=n,t}function xu(t,e,n){return e=rn(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Al=[],Fl=0,jr=null,ba=0,Hn=[],Nn=0,ce=null,he=1,Tl="";function Di(t,e){Al[Fl++]=ba,Al[Fl++]=jr,jr=t,ba=e}function Zc(t,e,n){Hn[Nn++]=he,Hn[Nn++]=Tl,Hn[Nn++]=ce,ce=t;var u=he;t=Tl;var a=32-Je(u)-1;u&=~(1<<a),n+=1;var f=32-Je(e)+a;if(30<f){var h=a-a%5;f=(u&(1<<h)-1).toString(32),u>>=h,a-=h,he=1<<32-Je(e)+a|n<<a|u,Tl=f+t}else he=1<<f|n<<a|u,Tl=t}function Vc(t){t.return!==null&&(Di(t,1),Zc(t,1,0))}function ya(t){for(;t===jr;)jr=Al[--Fl],Al[Fl]=null,ba=Al[--Fl],Al[Fl]=null;for(;t===ce;)ce=Hn[--Nn],Hn[Nn]=null,Tl=Hn[--Nn],Hn[Nn]=null,he=Hn[--Nn],Hn[Nn]=null}var ke=null,de=null,Mt=!1,Wn=null,fn=!1,Au=Error(g(519));function qn(t){var e=Error(g(418,""));throw $l(We(e,t)),Au}function Ws(t){var e=t.stateNode,n=t.type,u=t.memoizedProps;switch(e[Le]=t,e[ln]=u,n){case"dialog":zt("cancel",e),zt("close",e);break;case"iframe":case"object":case"embed":zt("load",e);break;case"video":case"audio":for(n=0;n<rr.length;n++)zt(rr[n],e);break;case"source":zt("error",e);break;case"img":case"image":case"link":zt("error",e),zt("load",e);break;case"details":zt("toggle",e);break;case"input":zt("invalid",e),xc(e,u.value,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name,!0),kr(e);break;case"select":zt("invalid",e);break;case"textarea":zt("invalid",e),Er(e,u.value,u.defaultValue,u.children),kr(e)}n=u.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||u.suppressHydrationWarning===!0||bd(e.textContent,n)?(u.popover!=null&&(zt("beforetoggle",e),zt("toggle",e)),u.onScroll!=null&&zt("scroll",e),u.onScrollEnd!=null&&zt("scrollend",e),u.onClick!=null&&(e.onclick=Nf),e=!0):e=!1,e||qn(t)}function Fs(t){for(ke=t.return;ke;)switch(ke.tag){case 5:case 13:fn=!1;return;case 27:case 3:fn=!0;return;default:ke=ke.return}}function Sa(t){if(t!==ke)return!1;if(!Mt)return Fs(t),Mt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Gf(t.type,t.memoizedProps)),n=!n),n&&de&&qn(t),Fs(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(g(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){de=el(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}de=null}}else e===27?(e=de,Vn(t.type)?(t=$o,$o=null,de=t):de=e):de=ke?el(t.stateNode.nextSibling):null;return!0}function _a(){de=ke=null,Mt=!1}function $s(){var t=Wn;return t!==null&&(pn===null?pn=t:pn.push.apply(pn,t),Wn=null),t}function $l(t){Wn===null?Wn=[t]:Wn.push(t)}var Ci=ae(null),Il=null,p=null;function Tn(t,e,n){Zt(Ci,e._currentValue),e._currentValue=n}function Gn(t){t._currentValue=Ci.current,fe(Ci)}function cn(t,e,n){for(;t!==null;){var u=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,u!==null&&(u.childLanes|=e)):u!==null&&(u.childLanes&e)!==e&&(u.childLanes|=e),t===n)break;t=t.return}}function At(t,e,n,u){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var f=a.dependencies;if(f!==null){var h=a.child;f=f.firstContext;t:for(;f!==null;){var v=f;f=a;for(var A=0;A<e.length;A++)if(v.context===e[A]){f.lanes|=n,v=f.alternate,v!==null&&(v.lanes|=n),cn(f.return,n,t),u||(h=null);break t}f=v.next}}else if(a.tag===18){if(h=a.return,h===null)throw Error(g(341));h.lanes|=n,f=h.alternate,f!==null&&(f.lanes|=n),cn(h,n,t),h=null}else h=a.child;if(h!==null)h.return=a;else for(h=a;h!==null;){if(h===t){h=null;break}if(a=h.sibling,a!==null){a.return=h.return,h=a;break}h=h.return}a=h}}function xa(t,e,n,u){t=null;for(var a=e,f=!1;a!==null;){if(!f){if((a.flags&524288)!==0)f=!0;else if((a.flags&262144)!==0)break}if(a.tag===10){var h=a.alternate;if(h===null)throw Error(g(387));if(h=h.memoizedProps,h!==null){var v=a.type;xn(a.pendingProps.value,h.value)||(t!==null?t.push(v):t=[v])}}else if(a===br.current){if(h=a.alternate,h===null)throw Error(g(387));h.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push($u):t=[$u])}a=a.return}t!==null&&At(e,t,n,u),e.flags|=262144}function Jr(t){for(t=t.firstContext;t!==null;){if(!xn(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Oi(t){Il=t,p=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ve(t){return Is(Il,t)}function Wr(t,e){return Il===null&&Oi(t),Is(t,e)}function Is(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},p===null){if(t===null)throw Error(g(308));p=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else p=p.next=e;return n}var pg=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,u){t.push(u)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},vg=o.unstable_scheduleCallback,bg=o.unstable_NormalPriority,ne={$$typeof:it,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Kc(){return{controller:new pg,data:new Map,refCount:0}}function Aa(t){t.refCount--,t.refCount===0&&vg(bg,function(){t.controller.abort()})}var Ta=null,jc=0,Tu=0,on=null;function yg(t,e){if(Ta===null){var n=Ta=[];jc=0,Tu=Lf(),on={status:"pending",value:void 0,then:function(u){n.push(u)}}}return jc++,e.then(Ps,Ps),e}function Ps(){if(--jc===0&&Ta!==null){on!==null&&(on.status="fulfilled");var t=Ta;Ta=null,Tu=0,on=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Sg(t,e){var n=[],u={status:"pending",value:null,reason:null,then:function(a){n.push(a)}};return t.then(function(){u.status="fulfilled",u.value=e;for(var a=0;a<n.length;a++)(0,n[a])(e)},function(a){for(u.status="rejected",u.reason=a,a=0;a<n.length;a++)(0,n[a])(void 0)}),u}var th=X.S;X.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&yg(t,e),th!==null&&th(t,e)};var Ri=ae(null);function wl(){var t=Ri.current;return t!==null?t:re.pooledCache}function Fr(t,e){e===null?Zt(Ri,Ri.current):Zt(Ri,e.pool)}function eh(){var t=wl();return t===null?null:{parent:ne._currentValue,pool:t}}var $e=Error(g(460)),nh=Error(g(474)),$r=Error(g(542)),Jc={then:function(){}};function lh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ir(){}function Wc(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ir,Ir),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,uh(t),t;default:if(typeof e.status=="string")e.then(Ir,Ir);else{if(t=re,t!==null&&100<t.shellSuspendCounter)throw Error(g(482));t=e,t.status="pending",t.then(function(u){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=u}},function(u){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=u}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,uh(t),t}throw wu=e,$e}}var wu=null;function ih(){if(wu===null)throw Error(g(459));var t=wu;return wu=null,t}function uh(t){if(t===$e||t===$r)throw Error(g(483))}var ol=!1;function Ui(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ku(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Pl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function kl(t,e,n){var u=t.updateQueue;if(u===null)return null;if(u=u.shared,(Jt&2)!==0){var a=u.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),u.pending=e,e=pa(t),ma(t,null,n),e}return Jn(t,u,e,n),pa(t)}function wa(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var u=e.lanes;u&=t.pendingLanes,n|=u,e.lanes=n,bs(t,n)}}function Fn(t,e){var n=t.updateQueue,u=t.alternate;if(u!==null&&(u=u.updateQueue,n===u)){var a=null,f=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};f===null?a=f=h:f=f.next=h,n=n.next}while(n!==null);f===null?a=f=e:f=f.next=e}else a=f=e;n={baseState:u.baseState,firstBaseUpdate:a,lastBaseUpdate:f,shared:u.shared,callbacks:u.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var ka=!1;function $n(){if(ka){var t=on;if(t!==null)throw t}}function Ze(t,e,n,u){ka=!1;var a=t.updateQueue;ol=!1;var f=a.firstBaseUpdate,h=a.lastBaseUpdate,v=a.shared.pending;if(v!==null){a.shared.pending=null;var A=v,U=A.next;A.next=null,h===null?f=U:h.next=U,h=A;var Y=t.alternate;Y!==null&&(Y=Y.updateQueue,v=Y.lastBaseUpdate,v!==h&&(v===null?Y.firstBaseUpdate=U:v.next=U,Y.lastBaseUpdate=A))}if(f!==null){var j=a.baseState;h=0,Y=U=A=null,v=f;do{var L=v.lane&-536870913,B=L!==v.lane;if(B?(Bt&L)===L:(u&L)===L){L!==0&&L===Tu&&(ka=!0),Y!==null&&(Y=Y.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});t:{var st=t,ot=v;L=e;var ee=n;switch(ot.tag){case 1:if(st=ot.payload,typeof st=="function"){j=st.call(ee,j,L);break t}j=st;break t;case 3:st.flags=st.flags&-65537|128;case 0:if(st=ot.payload,L=typeof st=="function"?st.call(ee,j,L):st,L==null)break t;j=q({},j,L);break t;case 2:ol=!0}}L=v.callback,L!==null&&(t.flags|=64,B&&(t.flags|=8192),B=a.callbacks,B===null?a.callbacks=[L]:B.push(L))}else B={lane:L,tag:v.tag,payload:v.payload,callback:v.callback,next:null},Y===null?(U=Y=B,A=j):Y=Y.next=B,h|=L;if(v=v.next,v===null){if(v=a.shared.pending,v===null)break;B=v,v=B.next,B.next=null,a.lastBaseUpdate=B,a.shared.pending=null}}while(!0);Y===null&&(A=j),a.baseState=A,a.firstBaseUpdate=U,a.lastBaseUpdate=Y,f===null&&(a.shared.lanes=0),fi|=h,t.lanes=h,t.memoizedState=j}}function ah(t,e){if(typeof t!="function")throw Error(g(191,t));t.call(e)}function Fc(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)ah(n[t],e)}var Bi=ae(null),ti=ae(0);function zl(t,e){t=Ll,Zt(ti,t),Zt(Bi,e),Ll=t|e.baseLanes}function Pr(){Zt(ti,Ll),Zt(Bi,Bi.current)}function $c(){Ll=ti.current,fe(Bi),fe(ti)}var In=0,xt=null,jt=null,Dt=null,zu=!1,Li=!1,Ve=!1,Eu=0,ei=0,Yn=null,rh=0;function Ot(){throw Error(g(321))}function za(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!xn(t[n],e[n]))return!1;return!0}function Ic(t,e,n,u,a,f){return In=f,xt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,X.H=t===null||t.memoizedState===null?Sh:vo,Ve=!1,f=n(u,a),Ve=!1,Li&&(f=ch(e,n,u,a)),fh(t),f}function fh(t){X.H=Na;var e=jt!==null&&jt.next!==null;if(In=0,Dt=jt=xt=null,zu=!1,ei=0,Yn=null,e)throw Error(g(300));t===null||Ne||(t=t.dependencies,t!==null&&Jr(t)&&(Ne=!0))}function ch(t,e,n,u){xt=t;var a=0;do{if(Li&&(Yn=null),ei=0,Li=!1,25<=a)throw Error(g(301));if(a+=1,Dt=jt=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}X.H=_h,f=e(n,u)}while(Li);return f}function Pc(){var t=X.H,e=t.useState()[0];return e=typeof e.then=="function"?Ea(e):e,t=t.useState()[0],(jt!==null?jt.memoizedState:null)!==t&&(xt.flags|=1024),e}function to(){var t=Eu!==0;return Eu=0,t}function Hi(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function tf(t){if(zu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}zu=!1}In=0,Dt=jt=xt=null,Li=!1,ei=Eu=0,Yn=null}function sn(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Dt===null?xt.memoizedState=Dt=t:Dt=Dt.next=t,Dt}function Ae(){if(jt===null){var t=xt.alternate;t=t!==null?t.memoizedState:null}else t=jt.next;var e=Dt===null?xt.memoizedState:Dt.next;if(e!==null)Dt=e,jt=t;else{if(t===null)throw xt.alternate===null?Error(g(467)):Error(g(310));jt=t,t={memoizedState:jt.memoizedState,baseState:jt.baseState,baseQueue:jt.baseQueue,queue:jt.queue,next:null},Dt===null?xt.memoizedState=Dt=t:Dt=Dt.next=t}return Dt}function Ni(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ea(t){var e=ei;return ei+=1,Yn===null&&(Yn=[]),t=Wc(Yn,t,e),e=xt,(Dt===null?e.memoizedState:Dt.next)===null&&(e=e.alternate,X.H=e===null||e.memoizedState===null?Sh:vo),t}function ef(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Ea(t);if(t.$$typeof===it)return ve(t)}throw Error(g(438,String(t)))}function Ma(t){var e=null,n=xt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var u=xt.alternate;u!==null&&(u=u.updateQueue,u!==null&&(u=u.memoCache,u!=null&&(e={data:u.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Ni(),xt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),u=0;u<t;u++)n[u]=K;return e.index++,n}function sl(t,e){return typeof e=="function"?e(t):e}function nf(t){var e=Ae();return eo(e,jt,t)}function eo(t,e,n){var u=t.queue;if(u===null)throw Error(g(311));u.lastRenderedReducer=n;var a=t.baseQueue,f=u.pending;if(f!==null){if(a!==null){var h=a.next;a.next=f.next,f.next=h}e.baseQueue=a=f,u.pending=null}if(f=t.baseState,a===null)t.memoizedState=f;else{e=a.next;var v=h=null,A=null,U=e,Y=!1;do{var j=U.lane&-536870913;if(j!==U.lane?(Bt&j)===j:(In&j)===j){var L=U.revertLane;if(L===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null}),j===Tu&&(Y=!0);else if((In&L)===L){U=U.next,L===Tu&&(Y=!0);continue}else j={lane:0,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},A===null?(v=A=j,h=f):A=A.next=j,xt.lanes|=L,fi|=L;j=U.action,Ve&&n(f,j),f=U.hasEagerState?U.eagerState:n(f,j)}else L={lane:j,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},A===null?(v=A=L,h=f):A=A.next=L,xt.lanes|=j,fi|=j;U=U.next}while(U!==null&&U!==e);if(A===null?h=f:A.next=v,!xn(f,t.memoizedState)&&(Ne=!0,Y&&(n=on,n!==null)))throw n;t.memoizedState=f,t.baseState=h,t.baseQueue=A,u.lastRenderedState=f}return a===null&&(u.lanes=0),[t.memoizedState,u.dispatch]}function no(t){var e=Ae(),n=e.queue;if(n===null)throw Error(g(311));n.lastRenderedReducer=t;var u=n.dispatch,a=n.pending,f=e.memoizedState;if(a!==null){n.pending=null;var h=a=a.next;do f=t(f,h.action),h=h.next;while(h!==a);xn(f,e.memoizedState)||(Ne=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),n.lastRenderedState=f}return[f,u]}function lo(t,e,n){var u=xt,a=Ae(),f=Mt;if(f){if(n===void 0)throw Error(g(407));n=n()}else n=e();var h=!xn((jt||a).memoizedState,n);h&&(a.memoizedState=n,Ne=!0),a=a.queue;var v=uf.bind(null,u,a,t);if(Oa(2048,8,v,[t]),a.getSnapshot!==e||h||Dt!==null&&Dt.memoizedState.tag&1){if(u.flags|=2048,Mu(9,of(),oh.bind(null,u,a,n,e),null),re===null)throw Error(g(349));f||(In&124)!==0||lf(u,e,n)}return n}function lf(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=xt.updateQueue,e===null?(e=Ni(),xt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function oh(t,e,n,u){e.value=n,e.getSnapshot=u,io(e)&&uo(t)}function uf(t,e,n){return n(function(){io(e)&&uo(t)})}function io(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!xn(t,n)}catch{return!0}}function uo(t){var e=ki(t,2);e!==null&&Mn(e,t,2)}function af(t){var e=sn();if(typeof t=="function"){var n=t;if(t=n(),Ve){yn(!0);try{n()}finally{yn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:t},e}function Da(t,e,n,u){return t.baseState=n,eo(t,jt,typeof u=="function"?u:sl)}function _g(t,e,n,u,a){if(gf(t))throw Error(g(485));if(t=e.action,t!==null){var f={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){f.listeners.push(h)}};X.T!==null?n(!0):f.isTransition=!1,u(f),n=e.pending,n===null?(f.next=e.pending=f,ao(e,f)):(f.next=n.next,e.pending=n.next=f)}}function ao(t,e){var n=e.action,u=e.payload,a=t.state;if(e.isTransition){var f=X.T,h={};X.T=h;try{var v=n(a,u),A=X.S;A!==null&&A(h,v),ro(t,e,v)}catch(U){rf(t,e,U)}finally{X.T=f}}else try{f=n(a,u),ro(t,e,f)}catch(U){rf(t,e,U)}}function ro(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(u){sh(t,e,u)},function(u){return rf(t,e,u)}):sh(t,e,n)}function sh(t,e,n){e.status="fulfilled",e.value=n,hh(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,ao(t,n)))}function rf(t,e,n){var u=t.pending;if(t.pending=null,u!==null){u=u.next;do e.status="rejected",e.reason=n,hh(e),e=e.next;while(e!==u)}t.action=null}function hh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function ff(t,e){return e}function fo(t,e){if(Mt){var n=re.formState;if(n!==null){t:{var u=xt;if(Mt){if(de){e:{for(var a=de,f=fn;a.nodeType!==8;){if(!f){a=null;break e}if(a=el(a.nextSibling),a===null){a=null;break e}}f=a.data,a=f==="F!"||f==="F"?a:null}if(a){de=el(a.nextSibling),u=a.data==="F!";break t}}qn(u)}u=!1}u&&(e=n[0])}}return n=sn(),n.memoizedState=n.baseState=e,u={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ff,lastRenderedState:e},n.queue=u,n=go.bind(null,xt,u),u.dispatch=n,u=af(!1),f=mo.bind(null,xt,!1,u.queue),u=sn(),a={state:e,dispatch:null,action:t,pending:null},u.queue=a,n=_g.bind(null,xt,a,f,n),a.dispatch=n,u.memoizedState=t,[e,n,!1]}function cf(t){var e=Ae();return dh(e,jt,t)}function dh(t,e,n){if(e=eo(t,e,ff)[0],t=nf(sl)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var u=Ea(e)}catch(h){throw h===$e?$r:h}else u=e;e=Ae();var a=e.queue,f=a.dispatch;return n!==e.memoizedState&&(xt.flags|=2048,Mu(9,of(),co.bind(null,a,n),null)),[u,f,t]}function co(t,e){t.action=e}function _t(t){var e=Ae(),n=jt;if(n!==null)return dh(e,n,t);Ae(),e=e.memoizedState,n=Ae();var u=n.queue.dispatch;return n.memoizedState=t,[e,u,!1]}function Mu(t,e,n,u){return t={tag:t,create:n,deps:u,inst:e,next:null},e=xt.updateQueue,e===null&&(e=Ni(),xt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(u=n.next,n.next=t,t.next=u,e.lastEffect=t),t}function of(){return{destroy:void 0,resource:void 0}}function Du(){return Ae().memoizedState}function Ca(t,e,n,u){var a=sn();u=u===void 0?null:u,xt.flags|=t,a.memoizedState=Mu(1|e,of(),n,u)}function Oa(t,e,n,u){var a=Ae();u=u===void 0?null:u;var f=a.memoizedState.inst;jt!==null&&u!==null&&za(u,jt.memoizedState.deps)?a.memoizedState=Mu(e,f,n,u):(xt.flags|=t,a.memoizedState=Mu(1|e,f,n,u))}function gh(t,e){Ca(8390656,8,t,e)}function wn(t,e){Oa(2048,8,t,e)}function mh(t,e){return Oa(4,2,t,e)}function Ra(t,e){return Oa(4,4,t,e)}function sf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function oo(t,e,n){n=n!=null?n.concat([t]):null,Oa(4,4,sf.bind(null,e,t),n)}function hf(){}function hn(t,e){var n=Ae();e=e===void 0?null:e;var u=n.memoizedState;return e!==null&&za(e,u[1])?u[0]:(n.memoizedState=[t,e],t)}function El(t,e){var n=Ae();e=e===void 0?null:e;var u=n.memoizedState;if(e!==null&&za(e,u[1]))return u[0];if(u=t(),Ve){yn(!0);try{t()}finally{yn(!1)}}return n.memoizedState=[u,e],u}function Ua(t,e,n){return n===void 0||(In&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Jh(),xt.lanes|=t,fi|=t,n)}function so(t,e,n,u){return xn(n,e)?n:Bi.current!==null?(t=Ua(t,n,u),xn(t,e)||(Ne=!0),t):(In&42)===0?(Ne=!0,t.memoizedState=n):(t=Jh(),xt.lanes|=t,fi|=t,e)}function Ba(t,e,n,u,a){var f=$.p;$.p=f!==0&&8>f?f:8;var h=X.T,v={};X.T=v,mo(t,!1,e,n);try{var A=a(),U=X.S;if(U!==null&&U(v,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var Y=Sg(A,u);qi(t,e,Y,je(t))}else qi(t,e,u,je(t))}catch(j){qi(t,e,{then:function(){},status:"rejected",reason:j},je())}finally{$.p=f,X.T=h}}function ph(){}function La(t,e,n,u){if(t.tag!==5)throw Error(g(476));var a=ho(t).queue;Ba(t,a,e,ft,n===null?ph:function(){return df(t),n(u)})}function ho(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ft,baseState:ft,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:ft},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function df(t){var e=ho(t).next.queue;qi(t,e,{},je())}function Ha(){return ve($u)}function Ml(){return Ae().memoizedState}function vh(){return Ae().memoizedState}function ni(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=je();t=Pl(n);var u=kl(e,t,n);u!==null&&(Mn(u,e,n),wa(u,e,n)),e={cache:Kc()},t.payload=e;return}e=e.return}}function bh(t,e,n){var u=je();n={lane:u,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},gf(t)?yh(e,n):(n=_u(t,e,n,u),n!==null&&(Mn(n,t,u),po(n,e,u)))}function go(t,e,n){var u=je();qi(t,e,n,u)}function qi(t,e,n,u){var a={lane:u,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(gf(t))yh(e,a);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var h=e.lastRenderedState,v=f(h,n);if(a.hasEagerState=!0,a.eagerState=v,xn(v,h))return Jn(t,e,a,0),re===null&&wi(),!1}catch{}finally{}if(n=_u(t,e,a,u),n!==null)return Mn(n,t,u),po(n,e,u),!0}return!1}function mo(t,e,n,u){if(u={lane:2,revertLane:Lf(),action:u,hasEagerState:!1,eagerState:null,next:null},gf(t)){if(e)throw Error(g(479))}else e=_u(t,n,u,2),e!==null&&Mn(e,t,2)}function gf(t){var e=t.alternate;return t===xt||e!==null&&e===xt}function yh(t,e){Li=zu=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function po(t,e,n){if((n&4194048)!==0){var u=e.lanes;u&=t.pendingLanes,n|=u,e.lanes=n,bs(t,n)}}var Na={readContext:ve,use:ef,useCallback:Ot,useContext:Ot,useEffect:Ot,useImperativeHandle:Ot,useLayoutEffect:Ot,useInsertionEffect:Ot,useMemo:Ot,useReducer:Ot,useRef:Ot,useState:Ot,useDebugValue:Ot,useDeferredValue:Ot,useTransition:Ot,useSyncExternalStore:Ot,useId:Ot,useHostTransitionStatus:Ot,useFormState:Ot,useActionState:Ot,useOptimistic:Ot,useMemoCache:Ot,useCacheRefresh:Ot},Sh={readContext:ve,use:ef,useCallback:function(t,e){return sn().memoizedState=[t,e===void 0?null:e],t},useContext:ve,useEffect:gh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Ca(4194308,4,sf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Ca(4194308,4,t,e)},useInsertionEffect:function(t,e){Ca(4,2,t,e)},useMemo:function(t,e){var n=sn();e=e===void 0?null:e;var u=t();if(Ve){yn(!0);try{t()}finally{yn(!1)}}return n.memoizedState=[u,e],u},useReducer:function(t,e,n){var u=sn();if(n!==void 0){var a=n(e);if(Ve){yn(!0);try{n(e)}finally{yn(!1)}}}else a=e;return u.memoizedState=u.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},u.queue=t,t=t.dispatch=bh.bind(null,xt,t),[u.memoizedState,t]},useRef:function(t){var e=sn();return t={current:t},e.memoizedState=t},useState:function(t){t=af(t);var e=t.queue,n=go.bind(null,xt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:hf,useDeferredValue:function(t,e){var n=sn();return Ua(n,t,e)},useTransition:function(){var t=af(!1);return t=Ba.bind(null,xt,t.queue,!0,!1),sn().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var u=xt,a=sn();if(Mt){if(n===void 0)throw Error(g(407));n=n()}else{if(n=e(),re===null)throw Error(g(349));(Bt&124)!==0||lf(u,e,n)}a.memoizedState=n;var f={value:n,getSnapshot:e};return a.queue=f,gh(uf.bind(null,u,f,t),[t]),u.flags|=2048,Mu(9,of(),oh.bind(null,u,f,n,e),null),n},useId:function(){var t=sn(),e=re.identifierPrefix;if(Mt){var n=Tl,u=he;n=(u&~(1<<32-Je(u)-1)).toString(32)+n,e="«"+e+"R"+n,n=Eu++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=rh++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ha,useFormState:fo,useActionState:fo,useOptimistic:function(t){var e=sn();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=mo.bind(null,xt,!0,n),n.dispatch=e,[t,e]},useMemoCache:Ma,useCacheRefresh:function(){return sn().memoizedState=ni.bind(null,xt)}},vo={readContext:ve,use:ef,useCallback:hn,useContext:ve,useEffect:wn,useImperativeHandle:oo,useInsertionEffect:mh,useLayoutEffect:Ra,useMemo:El,useReducer:nf,useRef:Du,useState:function(){return nf(sl)},useDebugValue:hf,useDeferredValue:function(t,e){var n=Ae();return so(n,jt.memoizedState,t,e)},useTransition:function(){var t=nf(sl)[0],e=Ae().memoizedState;return[typeof t=="boolean"?t:Ea(t),e]},useSyncExternalStore:lo,useId:Ml,useHostTransitionStatus:Ha,useFormState:cf,useActionState:cf,useOptimistic:function(t,e){var n=Ae();return Da(n,jt,t,e)},useMemoCache:Ma,useCacheRefresh:vh},_h={readContext:ve,use:ef,useCallback:hn,useContext:ve,useEffect:wn,useImperativeHandle:oo,useInsertionEffect:mh,useLayoutEffect:Ra,useMemo:El,useReducer:no,useRef:Du,useState:function(){return no(sl)},useDebugValue:hf,useDeferredValue:function(t,e){var n=Ae();return jt===null?Ua(n,t,e):so(n,jt.memoizedState,t,e)},useTransition:function(){var t=no(sl)[0],e=Ae().memoizedState;return[typeof t=="boolean"?t:Ea(t),e]},useSyncExternalStore:lo,useId:Ml,useHostTransitionStatus:Ha,useFormState:_t,useActionState:_t,useOptimistic:function(t,e){var n=Ae();return jt!==null?Da(n,jt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Ma,useCacheRefresh:vh},ze=null,dn=0;function mf(t){var e=dn;return dn+=1,ze===null&&(ze=[]),Wc(ze,t,e)}function qa(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Gi(t,e){throw e.$$typeof===M?Error(g(525)):(t=Object.prototype.toString.call(e),Error(g(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Yi(t){var e=t._init;return e(t._payload)}function bo(t){function e(C,E){if(t){var R=C.deletions;R===null?(C.deletions=[E],C.flags|=16):R.push(E)}}function n(C,E){if(!t)return null;for(;E!==null;)e(C,E),E=E.sibling;return null}function u(C){for(var E=new Map;C!==null;)C.key!==null?E.set(C.key,C):E.set(C.index,C),C=C.sibling;return E}function a(C,E){return C=Fe(C,E),C.index=0,C.sibling=null,C}function f(C,E,R){return C.index=R,t?(R=C.alternate,R!==null?(R=R.index,R<E?(C.flags|=67108866,E):R):(C.flags|=67108866,E)):(C.flags|=1048576,E)}function h(C){return t&&C.alternate===null&&(C.flags|=67108866),C}function v(C,E,R,Q){return E===null||E.tag!==6?(E=Qc(R,C.mode,Q),E.return=C,E):(E=a(E,R),E.return=C,E)}function A(C,E,R,Q){var lt=R.type;return lt===Z?Y(C,E,R.props.children,Q,R.key):E!==null&&(E.elementType===lt||typeof lt=="object"&&lt!==null&&lt.$$typeof===St&&Yi(lt)===E.type)?(E=a(E,R.props),qa(E,R),E.return=C,E):(E=Kr(R.type,R.key,R.props,null,C.mode,Q),qa(E,R),E.return=C,E)}function U(C,E,R,Q){return E===null||E.tag!==4||E.stateNode.containerInfo!==R.containerInfo||E.stateNode.implementation!==R.implementation?(E=xu(R,C.mode,Q),E.return=C,E):(E=a(E,R.children||[]),E.return=C,E)}function Y(C,E,R,Q,lt){return E===null||E.tag!==7?(E=Mi(R,C.mode,Q,lt),E.return=C,E):(E=a(E,R),E.return=C,E)}function j(C,E,R){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Qc(""+E,C.mode,R),E.return=C,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case N:return R=Kr(E.type,E.key,E.props,null,C.mode,R),qa(R,E),R.return=C,R;case F:return E=xu(E,C.mode,R),E.return=C,E;case St:var Q=E._init;return E=Q(E._payload),j(C,E,R)}if(ul(E)||Et(E))return E=Mi(E,C.mode,R,null),E.return=C,E;if(typeof E.then=="function")return j(C,mf(E),R);if(E.$$typeof===it)return j(C,Wr(C,E),R);Gi(C,E)}return null}function L(C,E,R,Q){var lt=E!==null?E.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return lt!==null?null:v(C,E,""+R,Q);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case N:return R.key===lt?A(C,E,R,Q):null;case F:return R.key===lt?U(C,E,R,Q):null;case St:return lt=R._init,R=lt(R._payload),L(C,E,R,Q)}if(ul(R)||Et(R))return lt!==null?null:Y(C,E,R,Q,null);if(typeof R.then=="function")return L(C,E,mf(R),Q);if(R.$$typeof===it)return L(C,E,Wr(C,R),Q);Gi(C,R)}return null}function B(C,E,R,Q,lt){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return C=C.get(R)||null,v(E,C,""+Q,lt);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case N:return C=C.get(Q.key===null?R:Q.key)||null,A(E,C,Q,lt);case F:return C=C.get(Q.key===null?R:Q.key)||null,U(E,C,Q,lt);case St:var Tt=Q._init;return Q=Tt(Q._payload),B(C,E,R,Q,lt)}if(ul(Q)||Et(Q))return C=C.get(R)||null,Y(E,C,Q,lt,null);if(typeof Q.then=="function")return B(C,E,R,mf(Q),lt);if(Q.$$typeof===it)return B(C,E,R,Wr(E,Q),lt);Gi(E,Q)}return null}function st(C,E,R,Q){for(var lt=null,Tt=null,rt=E,ht=E=0,Ye=null;rt!==null&&ht<R.length;ht++){rt.index>ht?(Ye=rt,rt=null):Ye=rt.sibling;var qt=L(C,rt,R[ht],Q);if(qt===null){rt===null&&(rt=Ye);break}t&&rt&&qt.alternate===null&&e(C,rt),E=f(qt,E,ht),Tt===null?lt=qt:Tt.sibling=qt,Tt=qt,rt=Ye}if(ht===R.length)return n(C,rt),Mt&&Di(C,ht),lt;if(rt===null){for(;ht<R.length;ht++)rt=j(C,R[ht],Q),rt!==null&&(E=f(rt,E,ht),Tt===null?lt=rt:Tt.sibling=rt,Tt=rt);return Mt&&Di(C,ht),lt}for(rt=u(rt);ht<R.length;ht++)Ye=B(rt,C,ht,R[ht],Q),Ye!==null&&(t&&Ye.alternate!==null&&rt.delete(Ye.key===null?ht:Ye.key),E=f(Ye,E,ht),Tt===null?lt=Ye:Tt.sibling=Ye,Tt=Ye);return t&&rt.forEach(function(bi){return e(C,bi)}),Mt&&Di(C,ht),lt}function ot(C,E,R,Q){if(R==null)throw Error(g(151));for(var lt=null,Tt=null,rt=E,ht=E=0,Ye=null,qt=R.next();rt!==null&&!qt.done;ht++,qt=R.next()){rt.index>ht?(Ye=rt,rt=null):Ye=rt.sibling;var bi=L(C,rt,qt.value,Q);if(bi===null){rt===null&&(rt=Ye);break}t&&rt&&bi.alternate===null&&e(C,rt),E=f(bi,E,ht),Tt===null?lt=bi:Tt.sibling=bi,Tt=bi,rt=Ye}if(qt.done)return n(C,rt),Mt&&Di(C,ht),lt;if(rt===null){for(;!qt.done;ht++,qt=R.next())qt=j(C,qt.value,Q),qt!==null&&(E=f(qt,E,ht),Tt===null?lt=qt:Tt.sibling=qt,Tt=qt);return Mt&&Di(C,ht),lt}for(rt=u(rt);!qt.done;ht++,qt=R.next())qt=B(rt,C,ht,qt.value,Q),qt!==null&&(t&&qt.alternate!==null&&rt.delete(qt.key===null?ht:qt.key),E=f(qt,E,ht),Tt===null?lt=qt:Tt.sibling=qt,Tt=qt);return t&&rt.forEach(function(l0){return e(C,l0)}),Mt&&Di(C,ht),lt}function ee(C,E,R,Q){if(typeof R=="object"&&R!==null&&R.type===Z&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case N:t:{for(var lt=R.key;E!==null;){if(E.key===lt){if(lt=R.type,lt===Z){if(E.tag===7){n(C,E.sibling),Q=a(E,R.props.children),Q.return=C,C=Q;break t}}else if(E.elementType===lt||typeof lt=="object"&&lt!==null&&lt.$$typeof===St&&Yi(lt)===E.type){n(C,E.sibling),Q=a(E,R.props),qa(Q,R),Q.return=C,C=Q;break t}n(C,E);break}else e(C,E);E=E.sibling}R.type===Z?(Q=Mi(R.props.children,C.mode,Q,R.key),Q.return=C,C=Q):(Q=Kr(R.type,R.key,R.props,null,C.mode,Q),qa(Q,R),Q.return=C,C=Q)}return h(C);case F:t:{for(lt=R.key;E!==null;){if(E.key===lt)if(E.tag===4&&E.stateNode.containerInfo===R.containerInfo&&E.stateNode.implementation===R.implementation){n(C,E.sibling),Q=a(E,R.children||[]),Q.return=C,C=Q;break t}else{n(C,E);break}else e(C,E);E=E.sibling}Q=xu(R,C.mode,Q),Q.return=C,C=Q}return h(C);case St:return lt=R._init,R=lt(R._payload),ee(C,E,R,Q)}if(ul(R))return st(C,E,R,Q);if(Et(R)){if(lt=Et(R),typeof lt!="function")throw Error(g(150));return R=lt.call(R),ot(C,E,R,Q)}if(typeof R.then=="function")return ee(C,E,mf(R),Q);if(R.$$typeof===it)return ee(C,E,Wr(C,R),Q);Gi(C,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,E!==null&&E.tag===6?(n(C,E.sibling),Q=a(E,R),Q.return=C,C=Q):(n(C,E),Q=Qc(R,C.mode,Q),Q.return=C,C=Q),h(C)):n(C,E)}return function(C,E,R,Q){try{dn=0;var lt=ee(C,E,R,Q);return ze=null,lt}catch(rt){if(rt===$e||rt===$r)throw rt;var Tt=rn(29,rt,null,C.mode);return Tt.lanes=Q,Tt.return=C,Tt}finally{}}}var Xi=bo(!0),xh=bo(!1),kn=ae(null),Ie=null;function Xn(t){var e=t.alternate;Zt(be,be.current&1),Zt(kn,t),Ie===null&&(e===null||Bi.current!==null||e.memoizedState!==null)&&(Ie=t)}function Ah(t){if(t.tag===22){if(Zt(be,be.current),Zt(kn,t),Ie===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ie=t)}}else Dl()}function Dl(){Zt(be,be.current),Zt(kn,kn.current)}function hl(t){fe(kn),Ie===t&&(Ie=null),fe(be)}var be=ae(0);function Ga(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||tn(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Cu(t,e,n,u){e=t.memoizedState,n=n(u,e),n=n==null?e:q({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Ya={enqueueSetState:function(t,e,n){t=t._reactInternals;var u=je(),a=Pl(u);a.payload=e,n!=null&&(a.callback=n),e=kl(t,a,u),e!==null&&(Mn(e,t,u),wa(e,t,u))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var u=je(),a=Pl(u);a.tag=1,a.payload=e,n!=null&&(a.callback=n),e=kl(t,a,u),e!==null&&(Mn(e,t,u),wa(e,t,u))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=je(),u=Pl(n);u.tag=2,e!=null&&(u.callback=e),e=kl(t,u,n),e!==null&&(Mn(e,t,n),wa(e,t,n))}};function Xa(t,e,n,u,a,f,h){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(u,f,h):e.prototype&&e.prototype.isPureReactComponent?!cl(n,u)||!cl(a,f):!0}function Th(t,e,n,u){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,u),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,u),e.state!==t&&Ya.enqueueReplaceState(e,e.state,null)}function li(t,e){var n=e;if("ref"in e){n={};for(var u in e)u!=="ref"&&(n[u]=e[u])}if(t=t.defaultProps){n===e&&(n=q({},n));for(var a in t)n[a]===void 0&&(n[a]=t[a])}return n}var Qi=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function yo(t){Qi(t)}function pf(t){console.error(t)}function wh(t){Qi(t)}function Qa(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(u){setTimeout(function(){throw u})}}function dl(t,e,n){try{var u=t.onCaughtError;u(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function vf(t,e,n){return n=Pl(n),n.tag=3,n.payload={element:null},n.callback=function(){Qa(t,e)},n}function So(t){return t=Pl(t),t.tag=3,t}function kh(t,e,n,u){var a=n.type.getDerivedStateFromError;if(typeof a=="function"){var f=u.value;t.payload=function(){return a(f)},t.callback=function(){dl(e,n,u)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(t.callback=function(){dl(e,n,u),typeof a!="function"&&(ci===null?ci=new Set([this]):ci.add(this));var v=u.stack;this.componentDidCatch(u.value,{componentStack:v!==null?v:""})})}function zh(t,e,n,u,a){if(n.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){if(e=n.alternate,e!==null&&xa(e,n,a,!0),n=kn.current,n!==null){switch(n.tag){case 13:return Ie===null?qo():n.alternate===null&&ye===0&&(ye=3),n.flags&=-257,n.flags|=65536,n.lanes=a,u===Jc?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([u]):e.add(u),Xo(t,u,a)),!1;case 22:return n.flags|=65536,u===Jc?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([u])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([u]):n.add(u)),Xo(t,u,a)),!1}throw Error(g(435,n.tag))}return Xo(t,u,a),qo(),!1}if(Mt)return e=kn.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=a,u!==Au&&(t=Error(g(422),{cause:u}),$l(We(t,n)))):(u!==Au&&(e=Error(g(423),{cause:u}),$l(We(e,n))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,u=We(u,n),a=vf(t.stateNode,u,a),Fn(t,a),ye!==4&&(ye=2)),!1;var f=Error(g(520),{cause:u});if(f=We(f,n),Pa===null?Pa=[f]:Pa.push(f),ye!==4&&(ye=2),e===null)return!0;u=We(u,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=a&-a,n.lanes|=t,t=vf(n.stateNode,u,t),Fn(n,t),!1;case 1:if(e=n.type,f=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(ci===null||!ci.has(f))))return n.flags|=65536,a&=-a,n.lanes|=a,a=So(a),kh(a,t,n,u),Fn(n,a),!1}n=n.return}while(n!==null);return!1}var Eh=Error(g(461)),Ne=!1;function oe(t,e,n,u){e.child=t===null?xh(e,null,n,u):Xi(e,t.child,n,u)}function bf(t,e,n,u,a){n=n.render;var f=e.ref;if("ref"in u){var h={};for(var v in u)v!=="ref"&&(h[v]=u[v])}else h=u;return Oi(e),u=Ic(t,e,n,h,f,a),v=to(),t!==null&&!Ne?(Hi(t,e,a),Cl(t,e,a)):(Mt&&v&&Vc(e),e.flags|=1,oe(t,e,u,a),e.child)}function yf(t,e,n,u,a){if(t===null){var f=n.type;return typeof f=="function"&&!Ei(f)&&f.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=f,Sf(t,e,f,u,a)):(t=Kr(n.type,null,u,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!Pn(t,a)){var h=f.memoizedProps;if(n=n.compare,n=n!==null?n:cl,n(h,u)&&t.ref===e.ref)return Cl(t,e,a)}return e.flags|=1,t=Fe(f,u),t.ref=e.ref,t.return=e,e.child=t}function Sf(t,e,n,u,a){if(t!==null){var f=t.memoizedProps;if(cl(f,u)&&t.ref===e.ref)if(Ne=!1,e.pendingProps=u=f,Pn(t,a))(t.flags&131072)!==0&&(Ne=!0);else return e.lanes=t.lanes,Cl(t,e,a)}return Ou(t,e,n,u,a)}function Za(t,e,n){var u=e.pendingProps,a=u.children,f=t!==null?t.memoizedState:null;if(u.mode==="hidden"){if((e.flags&128)!==0){if(u=f!==null?f.baseLanes|n:n,t!==null){for(a=e.child=t.child,f=0;a!==null;)f=f|a.lanes|a.childLanes,a=a.sibling;e.childLanes=f&~u}else e.childLanes=0,e.child=null;return Zi(t,e,u,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Fr(e,f!==null?f.cachePool:null),f!==null?zl(e,f):Pr(),Ah(e);else return e.lanes=e.childLanes=536870912,Zi(t,e,f!==null?f.baseLanes|n:n,n)}else f!==null?(Fr(e,f.cachePool),zl(e,f),Dl(),e.memoizedState=null):(t!==null&&Fr(e,null),Pr(),Dl());return oe(t,e,a,n),e.child}function Zi(t,e,n,u){var a=wl();return a=a===null?null:{parent:ne._currentValue,pool:a},e.memoizedState={baseLanes:n,cachePool:a},t!==null&&Fr(e,null),Pr(),Ah(e),t!==null&&xa(t,e,u,!0),null}function ct(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(g(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Ou(t,e,n,u,a){return Oi(e),n=Ic(t,e,n,u,void 0,a),u=to(),t!==null&&!Ne?(Hi(t,e,a),Cl(t,e,a)):(Mt&&u&&Vc(e),e.flags|=1,oe(t,e,n,a),e.child)}function _f(t,e,n,u,a,f){return Oi(e),e.updateQueue=null,n=ch(e,u,n,a),fh(t),u=to(),t!==null&&!Ne?(Hi(t,e,f),Cl(t,e,f)):(Mt&&u&&Vc(e),e.flags|=1,oe(t,e,n,f),e.child)}function ii(t,e,n,u,a){if(Oi(e),e.stateNode===null){var f=zi,h=n.contextType;typeof h=="object"&&h!==null&&(f=ve(h)),f=new n(u,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=Ya,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=u,f.state=e.memoizedState,f.refs={},Ui(e),h=n.contextType,f.context=typeof h=="object"&&h!==null?ve(h):zi,f.state=e.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(Cu(e,n,h,u),f.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(h=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),h!==f.state&&Ya.enqueueReplaceState(f,f.state,null),Ze(e,u,f,a),$n(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),u=!0}else if(t===null){f=e.stateNode;var v=e.memoizedProps,A=li(n,v);f.props=A;var U=f.context,Y=n.contextType;h=zi,typeof Y=="object"&&Y!==null&&(h=ve(Y));var j=n.getDerivedStateFromProps;Y=typeof j=="function"||typeof f.getSnapshotBeforeUpdate=="function",v=e.pendingProps!==v,Y||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(v||U!==h)&&Th(e,f,u,h),ol=!1;var L=e.memoizedState;f.state=L,Ze(e,u,f,a),$n(),U=e.memoizedState,v||L!==U||ol?(typeof j=="function"&&(Cu(e,n,j,u),U=e.memoizedState),(A=ol||Xa(e,n,A,u,L,U,h))?(Y||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=u,e.memoizedState=U),f.props=u,f.state=U,f.context=h,u=A):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),u=!1)}else{f=e.stateNode,ku(t,e),h=e.memoizedProps,Y=li(n,h),f.props=Y,j=e.pendingProps,L=f.context,U=n.contextType,A=zi,typeof U=="object"&&U!==null&&(A=ve(U)),v=n.getDerivedStateFromProps,(U=typeof v=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(h!==j||L!==A)&&Th(e,f,u,A),ol=!1,L=e.memoizedState,f.state=L,Ze(e,u,f,a),$n();var B=e.memoizedState;h!==j||L!==B||ol||t!==null&&t.dependencies!==null&&Jr(t.dependencies)?(typeof v=="function"&&(Cu(e,n,v,u),B=e.memoizedState),(Y=ol||Xa(e,n,Y,u,L,B,A)||t!==null&&t.dependencies!==null&&Jr(t.dependencies))?(U||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(u,B,A),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(u,B,A)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||h===t.memoizedProps&&L===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||h===t.memoizedProps&&L===t.memoizedState||(e.flags|=1024),e.memoizedProps=u,e.memoizedState=B),f.props=u,f.state=B,f.context=A,u=Y):(typeof f.componentDidUpdate!="function"||h===t.memoizedProps&&L===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||h===t.memoizedProps&&L===t.memoizedState||(e.flags|=1024),u=!1)}return f=u,ct(t,e),u=(e.flags&128)!==0,f||u?(f=e.stateNode,n=u&&typeof n.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&u?(e.child=Xi(e,t.child,null,a),e.child=Xi(e,null,n,a)):oe(t,e,n,a),e.memoizedState=f.state,t=e.child):t=Cl(t,e,a),t}function Mh(t,e,n,u){return _a(),e.flags|=256,oe(t,e,n,u),e.child}var Va={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function xf(t){return{baseLanes:t,cachePool:eh()}}function qe(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Zn),t}function Dh(t,e,n){var u=e.pendingProps,a=!1,f=(e.flags&128)!==0,h;if((h=f)||(h=t!==null&&t.memoizedState===null?!1:(be.current&2)!==0),h&&(a=!0,e.flags&=-129),h=(e.flags&32)!==0,e.flags&=-33,t===null){if(Mt){if(a?Xn(e):Dl(),Mt){var v=de,A;if(A=v){t:{for(A=v,v=fn;A.nodeType!==8;){if(!v){v=null;break t}if(A=el(A.nextSibling),A===null){v=null;break t}}v=A}v!==null?(e.memoizedState={dehydrated:v,treeContext:ce!==null?{id:he,overflow:Tl}:null,retryLane:536870912,hydrationErrors:null},A=rn(18,null,null,0),A.stateNode=v,A.return=e,e.child=A,ke=e,de=null,A=!0):A=!1}A||qn(e)}if(v=e.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return tn(v)?e.lanes=32:e.lanes=536870912,null;hl(e)}return v=u.children,u=u.fallback,a?(Dl(),a=e.mode,v=Ka({mode:"hidden",children:v},a),u=Mi(u,a,n,null),v.return=e,u.return=e,v.sibling=u,e.child=v,a=e.child,a.memoizedState=xf(n),a.childLanes=qe(t,h,n),e.memoizedState=Va,u):(Xn(e),_o(e,v))}if(A=t.memoizedState,A!==null&&(v=A.dehydrated,v!==null)){if(f)e.flags&256?(Xn(e),e.flags&=-257,e=xo(t,e,n)):e.memoizedState!==null?(Dl(),e.child=t.child,e.flags|=128,e=null):(Dl(),a=u.fallback,v=e.mode,u=Ka({mode:"visible",children:u.children},v),a=Mi(a,v,n,null),a.flags|=2,u.return=e,a.return=e,u.sibling=a,e.child=u,Xi(e,t.child,null,n),u=e.child,u.memoizedState=xf(n),u.childLanes=qe(t,h,n),e.memoizedState=Va,e=a);else if(Xn(e),tn(v)){if(h=v.nextSibling&&v.nextSibling.dataset,h)var U=h.dgst;h=U,u=Error(g(419)),u.stack="",u.digest=h,$l({value:u,source:null,stack:null}),e=xo(t,e,n)}else if(Ne||xa(t,e,n,!1),h=(n&t.childLanes)!==0,Ne||h){if(h=re,h!==null&&(u=n&-n,u=(u&42)!==0?1:hc(u),u=(u&(h.suspendedLanes|n))!==0?0:u,u!==0&&u!==A.retryLane))throw A.retryLane=u,ki(t,u),Mn(h,t,u),Eh;v.data==="$?"||qo(),e=xo(t,e,n)}else v.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=A.treeContext,de=el(v.nextSibling),ke=e,Mt=!0,Wn=null,fn=!1,t!==null&&(Hn[Nn++]=he,Hn[Nn++]=Tl,Hn[Nn++]=ce,he=t.id,Tl=t.overflow,ce=e),e=_o(e,u.children),e.flags|=4096);return e}return a?(Dl(),a=u.fallback,v=e.mode,A=t.child,U=A.sibling,u=Fe(A,{mode:"hidden",children:u.children}),u.subtreeFlags=A.subtreeFlags&65011712,U!==null?a=Fe(U,a):(a=Mi(a,v,n,null),a.flags|=2),a.return=e,u.return=e,u.sibling=a,e.child=u,u=a,a=e.child,v=t.child.memoizedState,v===null?v=xf(n):(A=v.cachePool,A!==null?(U=ne._currentValue,A=A.parent!==U?{parent:U,pool:U}:A):A=eh(),v={baseLanes:v.baseLanes|n,cachePool:A}),a.memoizedState=v,a.childLanes=qe(t,h,n),e.memoizedState=Va,u):(Xn(e),n=t.child,t=n.sibling,n=Fe(n,{mode:"visible",children:u.children}),n.return=e,n.sibling=null,t!==null&&(h=e.deletions,h===null?(e.deletions=[t],e.flags|=16):h.push(t)),e.child=n,e.memoizedState=null,n)}function _o(t,e){return e=Ka({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ka(t,e){return t=rn(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function xo(t,e,n){return Xi(e,t.child,null,n),t=_o(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Ao(t,e,n){t.lanes|=e;var u=t.alternate;u!==null&&(u.lanes|=e),cn(t.return,e,n)}function To(t,e,n,u,a){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:u,tail:n,tailMode:a}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=u,f.tail=n,f.tailMode=a)}function Ch(t,e,n){var u=e.pendingProps,a=u.revealOrder,f=u.tail;if(oe(t,e,u.children,n),u=be.current,(u&2)!==0)u=u&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Ao(t,n,e);else if(t.tag===19)Ao(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}u&=1}switch(Zt(be,u),a){case"forwards":for(n=e.child,a=null;n!==null;)t=n.alternate,t!==null&&Ga(t)===null&&(a=n),n=n.sibling;n=a,n===null?(a=e.child,e.child=null):(a=n.sibling,n.sibling=null),To(e,!1,a,n,f);break;case"backwards":for(n=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&Ga(t)===null){e.child=a;break}t=a.sibling,a.sibling=n,n=a,a=t}To(e,!0,n,null,f);break;case"together":To(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Cl(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),fi|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(xa(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(g(153));if(e.child!==null){for(t=e.child,n=Fe(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Fe(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Pn(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Jr(t)))}function Pe(t,e,n){switch(e.tag){case 3:Gl(e,e.stateNode.containerInfo),Tn(e,ne,t.memoizedState.cache),_a();break;case 27:case 5:fc(e);break;case 4:Gl(e,e.stateNode.containerInfo);break;case 10:Tn(e,e.type,e.memoizedProps.value);break;case 13:var u=e.memoizedState;if(u!==null)return u.dehydrated!==null?(Xn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Dh(t,e,n):(Xn(e),t=Cl(t,e,n),t!==null?t.sibling:null);Xn(e);break;case 19:var a=(t.flags&128)!==0;if(u=(n&e.childLanes)!==0,u||(xa(t,e,n,!1),u=(n&e.childLanes)!==0),a){if(u)return Ch(t,e,n);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),Zt(be,be.current),u)break;return null;case 22:case 23:return e.lanes=0,Za(t,e,n);case 24:Tn(e,ne,t.memoizedState.cache)}return Cl(t,e,n)}function Af(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Ne=!0;else{if(!Pn(t,n)&&(e.flags&128)===0)return Ne=!1,Pe(t,e,n);Ne=(t.flags&131072)!==0}else Ne=!1,Mt&&(e.flags&1048576)!==0&&Zc(e,ba,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var u=e.elementType,a=u._init;if(u=a(u._payload),e.type=u,typeof u=="function")Ei(u)?(t=li(u,t),e.tag=1,e=ii(null,e,u,t,n)):(e.tag=0,e=Ou(null,e,u,t,n));else{if(u!=null){if(a=u.$$typeof,a===Ut){e.tag=11,e=bf(null,e,u,t,n);break t}else if(a===$t){e.tag=14,e=yf(null,e,u,t,n);break t}}throw e=en(u)||u,Error(g(306,e,""))}}return e;case 0:return Ou(t,e,e.type,e.pendingProps,n);case 1:return u=e.type,a=li(u,e.pendingProps),ii(t,e,u,a,n);case 3:t:{if(Gl(e,e.stateNode.containerInfo),t===null)throw Error(g(387));u=e.pendingProps;var f=e.memoizedState;a=f.element,ku(t,e),Ze(e,u,null,n);var h=e.memoizedState;if(u=h.cache,Tn(e,ne,u),u!==f.cache&&At(e,[ne],n,!0),$n(),u=h.element,f.isDehydrated)if(f={element:u,isDehydrated:!1,cache:h.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=Mh(t,e,u,n);break t}else if(u!==a){a=We(Error(g(424)),e),$l(a),e=Mh(t,e,u,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(de=el(t.firstChild),ke=e,Mt=!0,Wn=null,fn=!0,n=xh(e,null,u,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(_a(),u===a){e=Cl(t,e,n);break t}oe(t,e,u,n)}e=e.child}return e;case 26:return ct(t,e),t===null?(n=zd(e.type,null,e.pendingProps,null))?e.memoizedState=n:Mt||(n=e.type,t=e.pendingProps,u=ji(we.current).createElement(n),u[Le]=e,u[ln]=t,De(u,n,t),He(u),e.stateNode=u):e.memoizedState=zd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return fc(e),t===null&&Mt&&(u=e.stateNode=Td(e.type,e.pendingProps,we.current),ke=e,fn=!0,a=de,Vn(e.type)?($o=a,de=el(u.firstChild)):de=a),oe(t,e,e.pendingProps.children,n),ct(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Mt&&((a=u=de)&&(u=xd(u,e.type,e.pendingProps,fn),u!==null?(e.stateNode=u,ke=e,de=el(u.firstChild),fn=!1,a=!0):a=!1),a||qn(e)),fc(e),a=e.type,f=e.pendingProps,h=t!==null?t.memoizedProps:null,u=f.children,Gf(a,f)?u=null:h!==null&&Gf(a,h)&&(e.flags|=32),e.memoizedState!==null&&(a=Ic(t,e,Pc,null,null,n),$u._currentValue=a),ct(t,e),oe(t,e,u,n),e.child;case 6:return t===null&&Mt&&((t=n=de)&&(n=Xf(n,e.pendingProps,fn),n!==null?(e.stateNode=n,ke=e,de=null,t=!0):t=!1),t||qn(e)),null;case 13:return Dh(t,e,n);case 4:return Gl(e,e.stateNode.containerInfo),u=e.pendingProps,t===null?e.child=Xi(e,null,u,n):oe(t,e,u,n),e.child;case 11:return bf(t,e,e.type,e.pendingProps,n);case 7:return oe(t,e,e.pendingProps,n),e.child;case 8:return oe(t,e,e.pendingProps.children,n),e.child;case 12:return oe(t,e,e.pendingProps.children,n),e.child;case 10:return u=e.pendingProps,Tn(e,e.type,u.value),oe(t,e,u.children,n),e.child;case 9:return a=e.type._context,u=e.pendingProps.children,Oi(e),a=ve(a),u=u(a),e.flags|=1,oe(t,e,u,n),e.child;case 14:return yf(t,e,e.type,e.pendingProps,n);case 15:return Sf(t,e,e.type,e.pendingProps,n);case 19:return Ch(t,e,n);case 31:return u=e.pendingProps,n=e.mode,u={mode:u.mode,children:u.children},t===null?(n=Ka(u,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Fe(t.child,u),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Za(t,e,n);case 24:return Oi(e),u=ve(ne),t===null?(a=wl(),a===null&&(a=re,f=Kc(),a.pooledCache=f,f.refCount++,f!==null&&(a.pooledCacheLanes|=n),a=f),e.memoizedState={parent:u,cache:a},Ui(e),Tn(e,ne,a)):((t.lanes&n)!==0&&(ku(t,e),Ze(e,null,null,n),$n()),a=t.memoizedState,f=e.memoizedState,a.parent!==u?(a={parent:u,cache:u},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),Tn(e,ne,u)):(u=f.cache,Tn(e,ne,u),u!==a.cache&&At(e,[ne],n,!0))),oe(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(g(156,e.tag))}function Ol(t){t.flags|=4}function Tf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Ce(e)){if(e=kn.current,e!==null&&((Bt&4194048)===Bt?Ie!==null:(Bt&62914560)!==Bt&&(Bt&536870912)===0||e!==Ie))throw wu=Jc,nh;t.flags|=8192}}function wf(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?ps():536870912,t.lanes|=e,qu|=e)}function ja(t,e){if(!Mt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var u=null;n!==null;)n.alternate!==null&&(u=n),n=n.sibling;u===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:u.sibling=null}}function le(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,u=0;if(e)for(var a=t.child;a!==null;)n|=a.lanes|a.childLanes,u|=a.subtreeFlags&65011712,u|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)n|=a.lanes|a.childLanes,u|=a.subtreeFlags,u|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=u,t.childLanes=n,e}function Oh(t,e,n){var u=e.pendingProps;switch(ya(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return le(e),null;case 1:return le(e),null;case 3:return n=e.stateNode,u=null,t!==null&&(u=t.memoizedState.cache),e.memoizedState.cache!==u&&(e.flags|=2048),Gn(ne),al(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Sa(e)?Ol(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,$s())),le(e),null;case 26:return n=e.memoizedState,t===null?(Ol(e),n!==null?(le(e),Tf(e,n)):(le(e),e.flags&=-16777217)):n?n!==t.memoizedState?(Ol(e),le(e),Tf(e,n)):(le(e),e.flags&=-16777217):(t.memoizedProps!==u&&Ol(e),le(e),e.flags&=-16777217),null;case 27:Si(e),n=we.current;var a=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==u&&Ol(e);else{if(!u){if(e.stateNode===null)throw Error(g(166));return le(e),null}t=Te.current,Sa(e)?Ws(e):(t=Td(a,u,n),e.stateNode=t,Ol(e))}return le(e),null;case 5:if(Si(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==u&&Ol(e);else{if(!u){if(e.stateNode===null)throw Error(g(166));return le(e),null}if(t=Te.current,Sa(e))Ws(e);else{switch(a=ji(we.current),t){case 1:t=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof u.is=="string"?a.createElement("select",{is:u.is}):a.createElement("select"),u.multiple?t.multiple=!0:u.size&&(t.size=u.size);break;default:t=typeof u.is=="string"?a.createElement(n,{is:u.is}):a.createElement(n)}}t[Le]=e,t[ln]=u;t:for(a=e.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;a=a.return}a.sibling.return=a.return,a=a.sibling}e.stateNode=t;t:switch(De(t,n,u),n){case"button":case"input":case"select":case"textarea":t=!!u.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ol(e)}}return le(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==u&&Ol(e);else{if(typeof u!="string"&&e.stateNode===null)throw Error(g(166));if(t=we.current,Sa(e)){if(t=e.stateNode,n=e.memoizedProps,u=null,a=ke,a!==null)switch(a.tag){case 27:case 5:u=a.memoizedProps}t[Le]=e,t=!!(t.nodeValue===n||u!==null&&u.suppressHydrationWarning===!0||bd(t.nodeValue,n)),t||qn(e)}else t=ji(t).createTextNode(u),t[Le]=e,e.stateNode=t}return le(e),null;case 13:if(u=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=Sa(e),u!==null&&u.dehydrated!==null){if(t===null){if(!a)throw Error(g(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(g(317));a[Le]=e}else _a(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;le(e),a=!1}else a=$s(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(hl(e),e):(hl(e),null)}if(hl(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=u!==null,t=t!==null&&t.memoizedState!==null,n){u=e.child,a=null,u.alternate!==null&&u.alternate.memoizedState!==null&&u.alternate.memoizedState.cachePool!==null&&(a=u.alternate.memoizedState.cachePool.pool);var f=null;u.memoizedState!==null&&u.memoizedState.cachePool!==null&&(f=u.memoizedState.cachePool.pool),f!==a&&(u.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),wf(e,e.updateQueue),le(e),null;case 4:return al(),t===null&&jo(e.stateNode.containerInfo),le(e),null;case 10:return Gn(e.type),le(e),null;case 19:if(fe(be),a=e.memoizedState,a===null)return le(e),null;if(u=(e.flags&128)!==0,f=a.rendering,f===null)if(u)ja(a,!1);else{if(ye!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=Ga(t),f!==null){for(e.flags|=128,ja(a,!1),t=f.updateQueue,e.updateQueue=t,wf(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)va(n,t),n=n.sibling;return Zt(be,be.current&1|2),e.child}t=t.sibling}a.tail!==null&&Be()>Gu&&(e.flags|=128,u=!0,ja(a,!1),e.lanes=4194304)}else{if(!u)if(t=Ga(f),t!==null){if(e.flags|=128,u=!0,t=t.updateQueue,e.updateQueue=t,wf(e,t),ja(a,!0),a.tail===null&&a.tailMode==="hidden"&&!f.alternate&&!Mt)return le(e),null}else 2*Be()-a.renderingStartTime>Gu&&n!==536870912&&(e.flags|=128,u=!0,ja(a,!1),e.lanes=4194304);a.isBackwards?(f.sibling=e.child,e.child=f):(t=a.last,t!==null?t.sibling=f:e.child=f,a.last=f)}return a.tail!==null?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=Be(),e.sibling=null,t=be.current,Zt(be,u?t&1|2:t&1),e):(le(e),null);case 22:case 23:return hl(e),$c(),u=e.memoizedState!==null,t!==null?t.memoizedState!==null!==u&&(e.flags|=8192):u&&(e.flags|=8192),u?(n&536870912)!==0&&(e.flags&128)===0&&(le(e),e.subtreeFlags&6&&(e.flags|=8192)):le(e),n=e.updateQueue,n!==null&&wf(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),u=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(u=e.memoizedState.cachePool.pool),u!==n&&(e.flags|=2048),t!==null&&fe(Ri),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Gn(ne),le(e),null;case 25:return null;case 30:return null}throw Error(g(156,e.tag))}function Rh(t,e){switch(ya(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Gn(ne),al(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Si(e),null;case 13:if(hl(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(g(340));_a()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return fe(be),null;case 4:return al(),null;case 10:return Gn(e.type),null;case 22:case 23:return hl(e),$c(),t!==null&&fe(Ri),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Gn(ne),null;case 25:return null;default:return null}}function Uh(t,e){switch(ya(e),e.tag){case 3:Gn(ne),al();break;case 26:case 27:case 5:Si(e);break;case 4:al();break;case 13:hl(e);break;case 19:fe(be);break;case 10:Gn(e.type);break;case 22:case 23:hl(e),$c(),t!==null&&fe(Ri);break;case 24:Gn(ne)}}function Ja(t,e){try{var n=e.updateQueue,u=n!==null?n.lastEffect:null;if(u!==null){var a=u.next;n=a;do{if((n.tag&t)===t){u=void 0;var f=n.create,h=n.inst;u=f(),h.destroy=u}n=n.next}while(n!==a)}}catch(v){ie(e,e.return,v)}}function ui(t,e,n){try{var u=e.updateQueue,a=u!==null?u.lastEffect:null;if(a!==null){var f=a.next;u=f;do{if((u.tag&t)===t){var h=u.inst,v=h.destroy;if(v!==void 0){h.destroy=void 0,a=e;var A=n,U=v;try{U()}catch(Y){ie(a,A,Y)}}}u=u.next}while(u!==f)}}catch(Y){ie(e,e.return,Y)}}function Bh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Fc(e,n)}catch(u){ie(t,t.return,u)}}}function wo(t,e,n){n.props=li(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(u){ie(t,e,u)}}function Ru(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var u=t.stateNode;break;case 30:u=t.stateNode;break;default:u=t.stateNode}typeof n=="function"?t.refCleanup=n(u):n.current=u}}catch(a){ie(t,e,a)}}function gl(t,e){var n=t.ref,u=t.refCleanup;if(n!==null)if(typeof u=="function")try{u()}catch(a){ie(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(a){ie(t,e,a)}else n.current=null}function kf(t){var e=t.type,n=t.memoizedProps,u=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&u.focus();break t;case"img":n.src?u.src=n.src:n.srcSet&&(u.srcset=n.srcSet)}}catch(a){ie(t,t.return,a)}}function zf(t,e,n){try{var u=t.stateNode;Lg(u,t.type,n,e),u[ln]=e}catch(a){ie(t,t.return,a)}}function Uu(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Vn(t.type)||t.tag===4}function Wa(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Uu(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Vn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Ef(t,e,n){var u=t.tag;if(u===5||u===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Nf));else if(u!==4&&(u===27&&Vn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Ef(t,e,n),t=t.sibling;t!==null;)Ef(t,e,n),t=t.sibling}function Fa(t,e,n){var u=t.tag;if(u===5||u===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(u!==4&&(u===27&&Vn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Fa(t,e,n),t=t.sibling;t!==null;)Fa(t,e,n),t=t.sibling}function $a(t){var e=t.stateNode,n=t.memoizedProps;try{for(var u=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);De(e,u,n),e[Le]=t,e[ln]=n}catch(f){ie(t,t.return,f)}}var ml=!1,Vt=!1,Rl=!1,Lh=typeof WeakSet=="function"?WeakSet:Set,Ee=null;function xg(t,e){if(t=t.containerInfo,tl=Kf,t=js(t),Gc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var u=n.getSelection&&n.getSelection();if(u&&u.rangeCount!==0){n=u.anchorNode;var a=u.anchorOffset,f=u.focusNode;u=u.focusOffset;try{n.nodeType,f.nodeType}catch{n=null;break t}var h=0,v=-1,A=-1,U=0,Y=0,j=t,L=null;e:for(;;){for(var B;j!==n||a!==0&&j.nodeType!==3||(v=h+a),j!==f||u!==0&&j.nodeType!==3||(A=h+u),j.nodeType===3&&(h+=j.nodeValue.length),(B=j.firstChild)!==null;)L=j,j=B;for(;;){if(j===t)break e;if(L===n&&++U===a&&(v=h),L===f&&++Y===u&&(A=h),(B=j.nextSibling)!==null)break;j=L,L=j.parentNode}j=B}n=v===-1||A===-1?null:{start:v,end:A}}else n=null}n=n||{start:0,end:0}}else n=null;for(qf={focusedElem:t,selectionRange:n},Kf=!1,Ee=e;Ee!==null;)if(e=Ee,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ee=t;else for(;Ee!==null;){switch(e=Ee,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,n=e,a=f.memoizedProps,f=f.memoizedState,u=n.stateNode;try{var st=li(n.type,a,n.elementType===n.type);t=u.getSnapshotBeforeUpdate(st,f),u.__reactInternalSnapshotBeforeUpdate=t}catch(ot){ie(n,n.return,ot)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Fo(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Fo(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(g(163))}if(t=e.sibling,t!==null){t.return=e.return,Ee=t;break}Ee=e.return}}function Hh(t,e,n){var u=n.flags;switch(n.tag){case 0:case 11:case 15:ai(t,n),u&4&&Ja(5,n);break;case 1:if(ai(t,n),u&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(h){ie(n,n.return,h)}else{var a=li(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(h){ie(n,n.return,h)}}u&64&&Bh(n),u&512&&Ru(n,n.return);break;case 3:if(ai(t,n),u&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Fc(t,e)}catch(h){ie(n,n.return,h)}}break;case 27:e===null&&u&4&&$a(n);case 26:case 5:ai(t,n),e===null&&u&4&&kf(n),u&512&&Ru(n,n.return);break;case 12:ai(t,n);break;case 13:ai(t,n),u&4&&Gh(t,n),u&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Rf.bind(null,n),Ku(t,n))));break;case 22:if(u=n.memoizedState!==null||ml,!u){e=e!==null&&e.memoizedState!==null||Vt,a=ml;var f=Vt;ml=u,(Vt=e)&&!f?Bl(t,n,(n.subtreeFlags&8772)!==0):ai(t,n),ml=a,Vt=f}break;case 30:break;default:ai(t,n)}}function Nh(t){var e=t.alternate;e!==null&&(t.alternate=null,Nh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&gc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var ge=null,gn=!1;function Ul(t,e,n){for(n=n.child;n!==null;)qh(t,e,n),n=n.sibling}function qh(t,e,n){if(Xe&&typeof Xe.onCommitFiberUnmount=="function")try{Xe.onCommitFiberUnmount(ia,n)}catch{}switch(n.tag){case 26:Vt||gl(n,e),Ul(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Vt||gl(n,e);var u=ge,a=gn;Vn(n.type)&&(ge=n.stateNode,gn=!1),Ul(t,e,n),fr(n.stateNode),ge=u,gn=a;break;case 5:Vt||gl(n,e);case 6:if(u=ge,a=gn,ge=null,Ul(t,e,n),ge=u,gn=a,ge!==null)if(gn)try{(ge.nodeType===9?ge.body:ge.nodeName==="HTML"?ge.ownerDocument.body:ge).removeChild(n.stateNode)}catch(f){ie(n,e,f)}else try{ge.removeChild(n.stateNode)}catch(f){ie(n,e,f)}break;case 18:ge!==null&&(gn?(t=ge,Yf(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),pr(t)):Yf(ge,n.stateNode));break;case 4:u=ge,a=gn,ge=n.stateNode.containerInfo,gn=!0,Ul(t,e,n),ge=u,gn=a;break;case 0:case 11:case 14:case 15:Vt||ui(2,n,e),Vt||ui(4,n,e),Ul(t,e,n);break;case 1:Vt||(gl(n,e),u=n.stateNode,typeof u.componentWillUnmount=="function"&&wo(n,e,u)),Ul(t,e,n);break;case 21:Ul(t,e,n);break;case 22:Vt=(u=Vt)||n.memoizedState!==null,Ul(t,e,n),Vt=u;break;default:Ul(t,e,n)}}function Gh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{pr(t)}catch(n){ie(e,e.return,n)}}function Ag(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Lh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Lh),e;default:throw Error(g(435,t.tag))}}function ko(t,e){var n=Ag(t);e.forEach(function(u){var a=Mg.bind(null,t,u);n.has(u)||(n.add(u),u.then(a,a))})}function zn(t,e){var n=e.deletions;if(n!==null)for(var u=0;u<n.length;u++){var a=n[u],f=t,h=e,v=h;t:for(;v!==null;){switch(v.tag){case 27:if(Vn(v.type)){ge=v.stateNode,gn=!1;break t}break;case 5:ge=v.stateNode,gn=!1;break t;case 3:case 4:ge=v.stateNode.containerInfo,gn=!0;break t}v=v.return}if(ge===null)throw Error(g(160));qh(f,h,a),ge=null,gn=!1,f=a.alternate,f!==null&&(f.return=null),a.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)zo(e,t),e=e.sibling}var Qn=null;function zo(t,e){var n=t.alternate,u=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:zn(e,t),mn(t),u&4&&(ui(3,t,t.return),Ja(3,t),ui(5,t,t.return));break;case 1:zn(e,t),mn(t),u&512&&(Vt||n===null||gl(n,n.return)),u&64&&ml&&(t=t.updateQueue,t!==null&&(u=t.callbacks,u!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?u:n.concat(u))));break;case 26:var a=Qn;if(zn(e,t),mn(t),u&512&&(Vt||n===null||gl(n,n.return)),u&4){var f=n!==null?n.memoizedState:null;if(u=t.memoizedState,n===null)if(u===null)if(t.stateNode===null){t:{u=t.type,n=t.memoizedProps,a=a.ownerDocument||a;e:switch(u){case"title":f=a.getElementsByTagName("title")[0],(!f||f[ua]||f[Le]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=a.createElement(u),a.head.insertBefore(f,a.querySelector("head > title"))),De(f,u,n),f[Le]=t,He(f),u=f;break t;case"link":var h=Dd("link","href",a).get(u+(n.href||""));if(h){for(var v=0;v<h.length;v++)if(f=h[v],f.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&f.getAttribute("rel")===(n.rel==null?null:n.rel)&&f.getAttribute("title")===(n.title==null?null:n.title)&&f.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(v,1);break e}}f=a.createElement(u),De(f,u,n),a.head.appendChild(f);break;case"meta":if(h=Dd("meta","content",a).get(u+(n.content||""))){for(v=0;v<h.length;v++)if(f=h[v],f.getAttribute("content")===(n.content==null?null:""+n.content)&&f.getAttribute("name")===(n.name==null?null:n.name)&&f.getAttribute("property")===(n.property==null?null:n.property)&&f.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&f.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(v,1);break e}}f=a.createElement(u),De(f,u,n),a.head.appendChild(f);break;default:throw Error(g(468,u))}f[Le]=t,He(f),u=f}t.stateNode=u}else Cd(a,t.type,t.stateNode);else t.stateNode=Md(a,u,t.memoizedProps);else f!==u?(f===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):f.count--,u===null?Cd(a,t.type,t.stateNode):Md(a,u,t.memoizedProps)):u===null&&t.stateNode!==null&&zf(t,t.memoizedProps,n.memoizedProps)}break;case 27:zn(e,t),mn(t),u&512&&(Vt||n===null||gl(n,n.return)),n!==null&&u&4&&zf(t,t.memoizedProps,n.memoizedProps);break;case 5:if(zn(e,t),mn(t),u&512&&(Vt||n===null||gl(n,n.return)),t.flags&32){a=t.stateNode;try{Ql(a,"")}catch(B){ie(t,t.return,B)}}u&4&&t.stateNode!=null&&(a=t.memoizedProps,zf(t,a,n!==null?n.memoizedProps:a)),u&1024&&(Rl=!0);break;case 6:if(zn(e,t),mn(t),u&4){if(t.stateNode===null)throw Error(g(162));u=t.memoizedProps,n=t.stateNode;try{n.nodeValue=u}catch(B){ie(t,t.return,B)}}break;case 3:if(Fu=null,a=Qn,Qn=yt(e.containerInfo),zn(e,t),Qn=a,mn(t),u&4&&n!==null&&n.memoizedState.isDehydrated)try{pr(e.containerInfo)}catch(B){ie(t,t.return,B)}Rl&&(Rl=!1,Yh(t));break;case 4:u=Qn,Qn=yt(t.stateNode.containerInfo),zn(e,t),mn(t),Qn=u;break;case 12:zn(e,t),mn(t);break;case 13:zn(e,t),mn(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Uo=Be()),u&4&&(u=t.updateQueue,u!==null&&(t.updateQueue=null,ko(t,u)));break;case 22:a=t.memoizedState!==null;var A=n!==null&&n.memoizedState!==null,U=ml,Y=Vt;if(ml=U||a,Vt=Y||A,zn(e,t),Vt=Y,ml=U,mn(t),u&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(n===null||A||ml||Vt||Vi(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){A=n=e;try{if(f=A.stateNode,a)h=f.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{v=A.stateNode;var j=A.memoizedProps.style,L=j!=null&&j.hasOwnProperty("display")?j.display:null;v.style.display=L==null||typeof L=="boolean"?"":(""+L).trim()}}catch(B){ie(A,A.return,B)}}}else if(e.tag===6){if(n===null){A=e;try{A.stateNode.nodeValue=a?"":A.memoizedProps}catch(B){ie(A,A.return,B)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}u&4&&(u=t.updateQueue,u!==null&&(n=u.retryQueue,n!==null&&(u.retryQueue=null,ko(t,n))));break;case 19:zn(e,t),mn(t),u&4&&(u=t.updateQueue,u!==null&&(t.updateQueue=null,ko(t,u)));break;case 30:break;case 21:break;default:zn(e,t),mn(t)}}function mn(t){var e=t.flags;if(e&2){try{for(var n,u=t.return;u!==null;){if(Uu(u)){n=u;break}u=u.return}if(n==null)throw Error(g(160));switch(n.tag){case 27:var a=n.stateNode,f=Wa(t);Fa(t,f,a);break;case 5:var h=n.stateNode;n.flags&32&&(Ql(h,""),n.flags&=-33);var v=Wa(t);Fa(t,v,h);break;case 3:case 4:var A=n.stateNode.containerInfo,U=Wa(t);Ef(t,U,A);break;default:throw Error(g(161))}}catch(Y){ie(t,t.return,Y)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Yh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Yh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ai(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Hh(t,e.alternate,e),e=e.sibling}function Vi(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ui(4,e,e.return),Vi(e);break;case 1:gl(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&wo(e,e.return,n),Vi(e);break;case 27:fr(e.stateNode);case 26:case 5:gl(e,e.return),Vi(e);break;case 22:e.memoizedState===null&&Vi(e);break;case 30:Vi(e);break;default:Vi(e)}t=t.sibling}}function Bl(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var u=e.alternate,a=t,f=e,h=f.flags;switch(f.tag){case 0:case 11:case 15:Bl(a,f,n),Ja(4,f);break;case 1:if(Bl(a,f,n),u=f,a=u.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(U){ie(u,u.return,U)}if(u=f,a=u.updateQueue,a!==null){var v=u.stateNode;try{var A=a.shared.hiddenCallbacks;if(A!==null)for(a.shared.hiddenCallbacks=null,a=0;a<A.length;a++)ah(A[a],v)}catch(U){ie(u,u.return,U)}}n&&h&64&&Bh(f),Ru(f,f.return);break;case 27:$a(f);case 26:case 5:Bl(a,f,n),n&&u===null&&h&4&&kf(f),Ru(f,f.return);break;case 12:Bl(a,f,n);break;case 13:Bl(a,f,n),n&&h&4&&Gh(a,f);break;case 22:f.memoizedState===null&&Bl(a,f,n),Ru(f,f.return);break;case 30:break;default:Bl(a,f,n)}e=e.sibling}}function Eo(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Aa(n))}function Mo(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Aa(t))}function pl(t,e,n,u){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Xh(t,e,n,u),e=e.sibling}function Xh(t,e,n,u){var a=e.flags;switch(e.tag){case 0:case 11:case 15:pl(t,e,n,u),a&2048&&Ja(9,e);break;case 1:pl(t,e,n,u);break;case 3:pl(t,e,n,u),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Aa(t)));break;case 12:if(a&2048){pl(t,e,n,u),t=e.stateNode;try{var f=e.memoizedProps,h=f.id,v=f.onPostCommit;typeof v=="function"&&v(h,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(A){ie(e,e.return,A)}}else pl(t,e,n,u);break;case 13:pl(t,e,n,u);break;case 23:break;case 22:f=e.stateNode,h=e.alternate,e.memoizedState!==null?f._visibility&2?pl(t,e,n,u):Ia(t,e):f._visibility&2?pl(t,e,n,u):(f._visibility|=2,Bu(t,e,n,u,(e.subtreeFlags&10256)!==0)),a&2048&&Eo(h,e);break;case 24:pl(t,e,n,u),a&2048&&Mo(e.alternate,e);break;default:pl(t,e,n,u)}}function Bu(t,e,n,u,a){for(a=a&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,h=e,v=n,A=u,U=h.flags;switch(h.tag){case 0:case 11:case 15:Bu(f,h,v,A,a),Ja(8,h);break;case 23:break;case 22:var Y=h.stateNode;h.memoizedState!==null?Y._visibility&2?Bu(f,h,v,A,a):Ia(f,h):(Y._visibility|=2,Bu(f,h,v,A,a)),a&&U&2048&&Eo(h.alternate,h);break;case 24:Bu(f,h,v,A,a),a&&U&2048&&Mo(h.alternate,h);break;default:Bu(f,h,v,A,a)}e=e.sibling}}function Ia(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,u=e,a=u.flags;switch(u.tag){case 22:Ia(n,u),a&2048&&Eo(u.alternate,u);break;case 24:Ia(n,u),a&2048&&Mo(u.alternate,u);break;default:Ia(n,u)}e=e.sibling}}var Ke=8192;function Lu(t){if(t.subtreeFlags&Ke)for(t=t.child;t!==null;)Qh(t),t=t.sibling}function Qh(t){switch(t.tag){case 26:Lu(t),t.flags&Ke&&t.memoizedState!==null&&Jg(Qn,t.memoizedState,t.memoizedProps);break;case 5:Lu(t);break;case 3:case 4:var e=Qn;Qn=yt(t.stateNode.containerInfo),Lu(t),Qn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ke,Ke=16777216,Lu(t),Ke=e):Lu(t));break;default:Lu(t)}}function Zh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Hu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var u=e[n];Ee=u,Kh(u,t)}Zh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Vh(t),t=t.sibling}function Vh(t){switch(t.tag){case 0:case 11:case 15:Hu(t),t.flags&2048&&ui(9,t,t.return);break;case 3:Hu(t);break;case 12:Hu(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Mf(t)):Hu(t);break;default:Hu(t)}}function Mf(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var u=e[n];Ee=u,Kh(u,t)}Zh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ui(8,e,e.return),Mf(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Mf(e));break;default:Mf(e)}t=t.sibling}}function Kh(t,e){for(;Ee!==null;){var n=Ee;switch(n.tag){case 0:case 11:case 15:ui(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var u=n.memoizedState.cachePool.pool;u!=null&&u.refCount++}break;case 24:Aa(n.memoizedState.cache)}if(u=n.child,u!==null)u.return=n,Ee=u;else t:for(n=t;Ee!==null;){u=Ee;var a=u.sibling,f=u.return;if(Nh(u),u===n){Ee=null;break t}if(a!==null){a.return=f,Ee=a;break t}Ee=f}}}var Tg={getCacheForType:function(t){var e=ve(ne),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Do=typeof WeakMap=="function"?WeakMap:Map,Jt=0,re=null,kt=null,Bt=0,Wt=0,En=null,ri=!1,Nu=!1,Co=!1,Ll=0,ye=0,fi=0,Ki=0,Oo=0,Zn=0,qu=0,Pa=null,pn=null,Ro=!1,Uo=0,Gu=1/0,tr=null,ci=null,Qe=0,oi=null,Yu=null,Xu=0,Bo=0,Lo=null,jh=null,Qu=0,Ho=null;function je(){if((Jt&2)!==0&&Bt!==0)return Bt&-Bt;if(X.T!==null){var t=Tu;return t!==0?t:Lf()}return ys()}function Jh(){Zn===0&&(Zn=(Bt&536870912)===0||Mt?ms():536870912);var t=kn.current;return t!==null&&(t.flags|=32),Zn}function Mn(t,e,n){(t===re&&(Wt===2||Wt===9)||t.cancelPendingCommit!==null)&&(Zu(t,0),si(t,Bt,Zn,!1)),eu(t,n),((Jt&2)===0||t!==re)&&(t===re&&((Jt&2)===0&&(Ki|=n),ye===4&&si(t,Bt,Zn,!1)),vl(t))}function Wh(t,e,n){if((Jt&6)!==0)throw Error(g(327));var u=!n&&(e&124)===0&&(e&t.expiredLanes)===0||_i(t,e),a=u?zg(t,e):Go(t,e,!0),f=u;do{if(a===0){Nu&&!u&&si(t,e,0,!1);break}else{if(n=t.current.alternate,f&&!wg(n)){a=Go(t,e,!1),f=!1;continue}if(a===2){if(f=e,t.errorRecoveryDisabledLanes&f)var h=0;else h=t.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){e=h;t:{var v=t;a=Pa;var A=v.current.memoizedState.isDehydrated;if(A&&(Zu(v,h).flags|=256),h=Go(v,h,!1),h!==2){if(Co&&!A){v.errorRecoveryDisabledLanes|=f,Ki|=f,a=4;break t}f=pn,pn=a,f!==null&&(pn===null?pn=f:pn.push.apply(pn,f))}a=h}if(f=!1,a!==2)continue}}if(a===1){Zu(t,0),si(t,e,0,!0);break}t:{switch(u=t,f=a,f){case 0:case 1:throw Error(g(345));case 4:if((e&4194048)!==e)break;case 6:si(u,e,Zn,!ri);break t;case 2:pn=null;break;case 3:case 5:break;default:throw Error(g(329))}if((e&62914560)===e&&(a=Uo+300-Be(),10<a)){if(si(u,e,Zn,!ri),tu(u,0,!0)!==0)break t;u.timeoutHandle=yd(Fh.bind(null,u,n,pn,tr,Ro,e,Zn,Ki,qu,ri,f,2,-0,0),a);break t}Fh(u,n,pn,tr,Ro,e,Zn,Ki,qu,ri,f,0,-0,0)}}break}while(!0);vl(t)}function Fh(t,e,n,u,a,f,h,v,A,U,Y,j,L,B){if(t.timeoutHandle=-1,j=e.subtreeFlags,(j&8192||(j&16785408)===16785408)&&(Ge={stylesheets:null,count:0,unsuspend:jg},Qh(e),j=Wg(),j!==null)){t.cancelPendingCommit=j(er.bind(null,t,e,f,n,u,a,h,v,A,Y,1,L,B)),si(t,f,h,!U);return}er(t,e,f,n,u,a,h,v,A)}function wg(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var u=0;u<n.length;u++){var a=n[u],f=a.getSnapshot;a=a.value;try{if(!xn(f(),a))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function si(t,e,n,u){e&=~Oo,e&=~Ki,t.suspendedLanes|=e,t.pingedLanes&=~e,u&&(t.warmLanes|=e),u=t.expirationTimes;for(var a=e;0<a;){var f=31-Je(a),h=1<<f;u[f]=-1,a&=~h}n!==0&&vs(t,n,e)}function Df(){return(Jt&6)===0?(ir(0),!1):!0}function No(){if(kt!==null){if(Wt===0)var t=kt.return;else t=kt,p=Il=null,tf(t),ze=null,dn=0,t=kt;for(;t!==null;)Uh(t.alternate,t),t=t.return;kt=null}}function Zu(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Ng(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),No(),re=t,kt=n=Fe(t.current,null),Bt=e,Wt=0,En=null,ri=!1,Nu=_i(t,e),Co=!1,qu=Zn=Oo=Ki=fi=ye=0,pn=Pa=null,Ro=!1,(e&8)!==0&&(e|=e&32);var u=t.entangledLanes;if(u!==0)for(t=t.entanglements,u&=e;0<u;){var a=31-Je(u),f=1<<a;e|=t[a],u&=~f}return Ll=e,wi(),n}function $h(t,e){xt=null,X.H=Na,e===$e||e===$r?(e=ih(),Wt=3):e===nh?(e=ih(),Wt=4):Wt=e===Eh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,En=e,kt===null&&(ye=1,Qa(t,We(e,t.current)))}function Ih(){var t=X.H;return X.H=Na,t===null?Na:t}function Ph(){var t=X.A;return X.A=Tg,t}function qo(){ye=4,ri||(Bt&4194048)!==Bt&&kn.current!==null||(Nu=!0),(fi&134217727)===0&&(Ki&134217727)===0||re===null||si(re,Bt,Zn,!1)}function Go(t,e,n){var u=Jt;Jt|=2;var a=Ih(),f=Ph();(re!==t||Bt!==e)&&(tr=null,Zu(t,e)),e=!1;var h=ye;t:do try{if(Wt!==0&&kt!==null){var v=kt,A=En;switch(Wt){case 8:No(),h=6;break t;case 3:case 2:case 9:case 6:kn.current===null&&(e=!0);var U=Wt;if(Wt=0,En=null,Vu(t,v,A,U),n&&Nu){h=0;break t}break;default:U=Wt,Wt=0,En=null,Vu(t,v,A,U)}}kg(),h=ye;break}catch(Y){$h(t,Y)}while(!0);return e&&t.shellSuspendCounter++,p=Il=null,Jt=u,X.H=a,X.A=f,kt===null&&(re=null,Bt=0,wi()),h}function kg(){for(;kt!==null;)Yo(kt)}function zg(t,e){var n=Jt;Jt|=2;var u=Ih(),a=Ph();re!==t||Bt!==e?(tr=null,Gu=Be()+500,Zu(t,e)):Nu=_i(t,e);t:do try{if(Wt!==0&&kt!==null){e=kt;var f=En;e:switch(Wt){case 1:Wt=0,En=null,Vu(t,e,f,1);break;case 2:case 9:if(lh(f)){Wt=0,En=null,ed(e);break}e=function(){Wt!==2&&Wt!==9||re!==t||(Wt=7),vl(t)},f.then(e,e);break t;case 3:Wt=7;break t;case 4:Wt=5;break t;case 7:lh(f)?(Wt=0,En=null,ed(e)):(Wt=0,En=null,Vu(t,e,f,7));break;case 5:var h=null;switch(kt.tag){case 26:h=kt.memoizedState;case 5:case 27:var v=kt;if(!h||Ce(h)){Wt=0,En=null;var A=v.sibling;if(A!==null)kt=A;else{var U=v.return;U!==null?(kt=U,Cf(U)):kt=null}break e}}Wt=0,En=null,Vu(t,e,f,5);break;case 6:Wt=0,En=null,Vu(t,e,f,6);break;case 8:No(),ye=6;break t;default:throw Error(g(462))}}td();break}catch(Y){$h(t,Y)}while(!0);return p=Il=null,X.H=u,X.A=a,Jt=n,kt!==null?0:(re=null,Bt=0,wi(),ye)}function td(){for(;kt!==null&&!ta();)Yo(kt)}function Yo(t){var e=Af(t.alternate,t,Ll);t.memoizedProps=t.pendingProps,e===null?Cf(t):kt=e}function ed(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=_f(n,e,e.pendingProps,e.type,void 0,Bt);break;case 11:e=_f(n,e,e.pendingProps,e.type.render,e.ref,Bt);break;case 5:tf(e);default:Uh(n,e),e=kt=va(e,Ll),e=Af(n,e,Ll)}t.memoizedProps=t.pendingProps,e===null?Cf(t):kt=e}function Vu(t,e,n,u){p=Il=null,tf(e),ze=null,dn=0;var a=e.return;try{if(zh(t,a,e,n,Bt)){ye=1,Qa(t,We(n,t.current)),kt=null;return}}catch(f){if(a!==null)throw kt=a,f;ye=1,Qa(t,We(n,t.current)),kt=null;return}e.flags&32768?(Mt||u===1?t=!0:Nu||(Bt&536870912)!==0?t=!1:(ri=t=!0,(u===2||u===9||u===3||u===6)&&(u=kn.current,u!==null&&u.tag===13&&(u.flags|=16384))),nd(e,t)):Cf(e)}function Cf(t){var e=t;do{if((e.flags&32768)!==0){nd(e,ri);return}t=e.return;var n=Oh(e.alternate,e,Ll);if(n!==null){kt=n;return}if(e=e.sibling,e!==null){kt=e;return}kt=e=t}while(e!==null);ye===0&&(ye=5)}function nd(t,e){do{var n=Rh(t.alternate,t);if(n!==null){n.flags&=32767,kt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){kt=t;return}kt=t=n}while(t!==null);ye=6,kt=null}function er(t,e,n,u,a,f,h,v,A){t.cancelPendingCommit=null;do Of();while(Qe!==0);if((Jt&6)!==0)throw Error(g(327));if(e!==null){if(e===t.current)throw Error(g(177));if(f=e.lanes|e.childLanes,f|=Xc,$d(t,n,f,h,v,A),t===re&&(kt=re=null,Bt=0),Yu=e,oi=t,Xu=n,Bo=f,Lo=a,jh=u,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,od(nn,function(){return rd(),null})):(t.callbackNode=null,t.callbackPriority=0),u=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||u){u=X.T,X.T=null,a=$.p,$.p=2,h=Jt,Jt|=4;try{xg(t,e,n)}finally{Jt=h,$.p=a,X.T=u}}Qe=1,ld(),id(),ud()}}function ld(){if(Qe===1){Qe=0;var t=oi,e=Yu,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=X.T,X.T=null;var u=$.p;$.p=2;var a=Jt;Jt|=4;try{zo(e,t);var f=qf,h=js(t.containerInfo),v=f.focusedElem,A=f.selectionRange;if(h!==v&&v&&v.ownerDocument&&Ks(v.ownerDocument.documentElement,v)){if(A!==null&&Gc(v)){var U=A.start,Y=A.end;if(Y===void 0&&(Y=U),"selectionStart"in v)v.selectionStart=U,v.selectionEnd=Math.min(Y,v.value.length);else{var j=v.ownerDocument||document,L=j&&j.defaultView||window;if(L.getSelection){var B=L.getSelection(),st=v.textContent.length,ot=Math.min(A.start,st),ee=A.end===void 0?ot:Math.min(A.end,st);!B.extend&&ot>ee&&(h=ee,ee=ot,ot=h);var C=qc(v,ot),E=qc(v,ee);if(C&&E&&(B.rangeCount!==1||B.anchorNode!==C.node||B.anchorOffset!==C.offset||B.focusNode!==E.node||B.focusOffset!==E.offset)){var R=j.createRange();R.setStart(C.node,C.offset),B.removeAllRanges(),ot>ee?(B.addRange(R),B.extend(E.node,E.offset)):(R.setEnd(E.node,E.offset),B.addRange(R))}}}}for(j=[],B=v;B=B.parentNode;)B.nodeType===1&&j.push({element:B,left:B.scrollLeft,top:B.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<j.length;v++){var Q=j[v];Q.element.scrollLeft=Q.left,Q.element.scrollTop=Q.top}}Kf=!!tl,qf=tl=null}finally{Jt=a,$.p=u,X.T=n}}t.current=e,Qe=2}}function id(){if(Qe===2){Qe=0;var t=oi,e=Yu,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=X.T,X.T=null;var u=$.p;$.p=2;var a=Jt;Jt|=4;try{Hh(t,e.alternate,e)}finally{Jt=a,$.p=u,X.T=n}}Qe=3}}function ud(){if(Qe===4||Qe===3){Qe=0,Jd();var t=oi,e=Yu,n=Xu,u=jh;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Qe=5:(Qe=0,Yu=oi=null,ad(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&(ci=null),_r(n),e=e.stateNode,Xe&&typeof Xe.onCommitFiberRoot=="function")try{Xe.onCommitFiberRoot(ia,e,void 0,(e.current.flags&128)===128)}catch{}if(u!==null){e=X.T,a=$.p,$.p=2,X.T=null;try{for(var f=t.onRecoverableError,h=0;h<u.length;h++){var v=u[h];f(v.value,{componentStack:v.stack})}}finally{X.T=e,$.p=a}}(Xu&3)!==0&&Of(),vl(t),a=t.pendingLanes,(n&4194090)!==0&&(a&42)!==0?t===Ho?Qu++:(Qu=0,Ho=t):Qu=0,ir(0)}}function ad(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Aa(e)))}function Of(t){return ld(),id(),ud(),rd()}function rd(){if(Qe!==5)return!1;var t=oi,e=Bo;Bo=0;var n=_r(Xu),u=X.T,a=$.p;try{$.p=32>n?32:n,X.T=null,n=Lo,Lo=null;var f=oi,h=Xu;if(Qe=0,Yu=oi=null,Xu=0,(Jt&6)!==0)throw Error(g(331));var v=Jt;if(Jt|=4,Vh(f.current),Xh(f,f.current,h,n),Jt=v,ir(0,!1),Xe&&typeof Xe.onPostCommitFiberRoot=="function")try{Xe.onPostCommitFiberRoot(ia,f)}catch{}return!0}finally{$.p=a,X.T=u,ad(t,e)}}function fd(t,e,n){e=We(n,e),e=vf(t.stateNode,e,2),t=kl(t,e,2),t!==null&&(eu(t,2),vl(t))}function ie(t,e,n){if(t.tag===3)fd(t,t,n);else for(;e!==null;){if(e.tag===3){fd(e,t,n);break}else if(e.tag===1){var u=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(ci===null||!ci.has(u))){t=We(n,t),n=So(2),u=kl(e,n,2),u!==null&&(kh(n,u,e,t),eu(u,2),vl(u));break}}e=e.return}}function Xo(t,e,n){var u=t.pingCache;if(u===null){u=t.pingCache=new Do;var a=new Set;u.set(e,a)}else a=u.get(e),a===void 0&&(a=new Set,u.set(e,a));a.has(n)||(Co=!0,a.add(n),t=Eg.bind(null,t,e,n),e.then(t,t))}function Eg(t,e,n){var u=t.pingCache;u!==null&&u.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,re===t&&(Bt&n)===n&&(ye===4||ye===3&&(Bt&62914560)===Bt&&300>Be()-Uo?(Jt&2)===0&&Zu(t,0):Oo|=n,qu===Bt&&(qu=0)),vl(t)}function cd(t,e){e===0&&(e=ps()),t=ki(t,e),t!==null&&(eu(t,e),vl(t))}function Rf(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),cd(t,n)}function Mg(t,e){var n=0;switch(t.tag){case 13:var u=t.stateNode,a=t.memoizedState;a!==null&&(n=a.retryLane);break;case 19:u=t.stateNode;break;case 22:u=t.stateNode._retryCache;break;default:throw Error(g(314))}u!==null&&u.delete(e),cd(t,n)}function od(t,e){return yr(t,e)}var nr=null,hi=null,Uf=!1,lr=!1,Bf=!1,di=0;function vl(t){t!==hi&&t.next===null&&(hi===null?nr=hi=t:hi=hi.next=t),lr=!0,Uf||(Uf=!0,Qo())}function ir(t,e){if(!Bf&&lr){Bf=!0;do for(var n=!1,u=nr;u!==null;){if(t!==0){var a=u.pendingLanes;if(a===0)var f=0;else{var h=u.suspendedLanes,v=u.pingedLanes;f=(1<<31-Je(42|t)+1)-1,f&=a&~(h&~v),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(n=!0,hd(u,f))}else f=Bt,f=tu(u,u===re?f:0,u.cancelPendingCommit!==null||u.timeoutHandle!==-1),(f&3)===0||_i(u,f)||(n=!0,hd(u,f));u=u.next}while(n);Bf=!1}}function Dg(){ur()}function ur(){lr=Uf=!1;var t=0;di!==0&&(Hg()&&(t=di),di=0);for(var e=Be(),n=null,u=nr;u!==null;){var a=u.next,f=ar(u,e);f===0?(u.next=null,n===null?nr=a:n.next=a,a===null&&(hi=n)):(n=u,(t!==0||(f&3)!==0)&&(lr=!0)),u=a}ir(t)}function ar(t,e){for(var n=t.suspendedLanes,u=t.pingedLanes,a=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var h=31-Je(f),v=1<<h,A=a[h];A===-1?((v&n)===0||(v&u)!==0)&&(a[h]=Fd(v,e)):A<=e&&(t.expiredLanes|=v),f&=~v}if(e=re,n=Bt,n=tu(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),u=t.callbackNode,n===0||t===e&&(Wt===2||Wt===9)||t.cancelPendingCommit!==null)return u!==null&&u!==null&&bn(u),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||_i(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(u!==null&&bn(u),_r(n)){case 2:case 8:n=$i;break;case 32:n=nn;break;case 268435456:n=na;break;default:n=nn}return u=sd.bind(null,t),n=yr(n,u),t.callbackPriority=e,t.callbackNode=n,e}return u!==null&&u!==null&&bn(u),t.callbackPriority=2,t.callbackNode=null,2}function sd(t,e){if(Qe!==0&&Qe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Of()&&t.callbackNode!==n)return null;var u=Bt;return u=tu(t,t===re?u:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),u===0?null:(Wh(t,u,e),ar(t,Be()),t.callbackNode!=null&&t.callbackNode===n?sd.bind(null,t):null)}function hd(t,e){if(Of())return null;Wh(t,e,!0)}function Qo(){qg(function(){(Jt&6)!==0?yr(gs,Dg):ur()})}function Lf(){return di===0&&(di=ms()),di}function dd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:fa(""+t)}function gd(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Cg(t,e,n,u,a){if(e==="submit"&&n&&n.stateNode===a){var f=dd((a[ln]||null).action),h=u.submitter;h&&(e=(e=h[ln]||null)?dd(e.formAction):h.getAttribute("formAction"),e!==null&&(f=e,h=null));var v=new Cr("action","action",null,u,a);t.push({event:v,listeners:[{instance:null,listener:function(){if(u.defaultPrevented){if(di!==0){var A=h?gd(a,h):new FormData(a);La(n,{pending:!0,data:A,method:a.method,action:f},null,A)}}else typeof f=="function"&&(v.preventDefault(),A=h?gd(a,h):new FormData(a),La(n,{pending:!0,data:A,method:a.method,action:f},f,A))},currentTarget:a}]})}}for(var Zo=0;Zo<Yt.length;Zo++){var Vo=Yt[Zo],Og=Vo.toLowerCase(),Rg=Vo[0].toUpperCase()+Vo.slice(1);jn(Og,"on"+Rg)}jn(Qr,"onAnimationEnd"),jn(Js,"onAnimationIteration"),jn(An,"onAnimationStart"),jn("dblclick","onDoubleClick"),jn("focusin","onFocus"),jn("focusout","onBlur"),jn(Zr,"onTransitionRun"),jn(mg,"onTransitionStart"),jn(yu,"onTransitionCancel"),jn(da,"onTransitionEnd"),au("onMouseEnter",["mouseout","mouseover"]),au("onMouseLeave",["mouseout","mouseover"]),au("onPointerEnter",["pointerout","pointerover"]),au("onPointerLeave",["pointerout","pointerover"]),xi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),xi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),xi("onBeforeInput",["compositionend","keypress","textInput","paste"]),xi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),xi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),xi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var rr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ug=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(rr));function md(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var u=t[n],a=u.event;u=u.listeners;t:{var f=void 0;if(e)for(var h=u.length-1;0<=h;h--){var v=u[h],A=v.instance,U=v.currentTarget;if(v=v.listener,A!==f&&a.isPropagationStopped())break t;f=v,a.currentTarget=U;try{f(a)}catch(Y){Qi(Y)}a.currentTarget=null,f=A}else for(h=0;h<u.length;h++){if(v=u[h],A=v.instance,U=v.currentTarget,v=v.listener,A!==f&&a.isPropagationStopped())break t;f=v,a.currentTarget=U;try{f(a)}catch(Y){Qi(Y)}a.currentTarget=null,f=A}}}}function zt(t,e){var n=e[dc];n===void 0&&(n=e[dc]=new Set);var u=t+"__bubble";n.has(u)||(pd(e,t,2,!1),n.add(u))}function Ko(t,e,n){var u=0;e&&(u|=4),pd(n,t,u,e)}var vn="_reactListening"+Math.random().toString(36).slice(2);function jo(t){if(!t[vn]){t[vn]=!0,_s.forEach(function(n){n!=="selectionchange"&&(Ug.has(n)||Ko(n,!1,t),Ko(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[vn]||(e[vn]=!0,Ko("selectionchange",!1,e))}}function pd(t,e,n,u){switch(Bd(e)){case 2:var a=Ig;break;case 8:a=Iu;break;default:a=ls}n=a.bind(null,e,n,t),a=void 0,!oa||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),u?a!==void 0?t.addEventListener(e,n,{capture:!0,passive:a}):t.addEventListener(e,n,!0):a!==void 0?t.addEventListener(e,n,{passive:a}):t.addEventListener(e,n,!1)}function Hl(t,e,n,u,a){var f=u;if((e&1)===0&&(e&2)===0&&u!==null)t:for(;;){if(u===null)return;var h=u.tag;if(h===3||h===4){var v=u.stateNode.containerInfo;if(v===a)break;if(h===4)for(h=u.return;h!==null;){var A=h.tag;if((A===3||A===4)&&h.stateNode.containerInfo===a)return;h=h.return}for(;v!==null;){if(h=nu(v),h===null)return;if(A=h.tag,A===5||A===6||A===26||A===27){u=f=h;continue t}v=v.parentNode}}u=u.return}zs(function(){var U=f,Y=zc(n),j=[];t:{var L=ga.get(t);if(L!==void 0){var B=Cr,st=t;switch(t){case"keypress":if(Pt(n)===0)break t;case"keydown":case"keyup":B=Rc;break;case"focusin":st="focus",B=Rr;break;case"focusout":st="blur",B=Rr;break;case"beforeblur":case"afterblur":B=Rr;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":B=Vl;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":B=Oc;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":B=ag;break;case Qr:case Js:case An:B=Os;break;case da:B=rg;break;case"scroll":case"scrollend":B=lg;break;case"wheel":B=cg;break;case"copy":case"cut":case"paste":B=Us;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":B=xl;break;case"toggle":case"beforetoggle":B=Br}var ot=(e&4)!==0,ee=!ot&&(t==="scroll"||t==="scrollend"),C=ot?L!==null?L+"Capture":null:L;ot=[];for(var E=U,R;E!==null;){var Q=E;if(R=Q.stateNode,Q=Q.tag,Q!==5&&Q!==26&&Q!==27||R===null||C===null||(Q=ca(E,C),Q!=null&&ot.push(vt(E,Q,R))),ee)break;E=E.return}0<ot.length&&(L=new B(L,st,null,n,Y),j.push({event:L,listeners:ot}))}}if((e&7)===0){t:{if(L=t==="mouseover"||t==="pointerover",B=t==="mouseout"||t==="pointerout",L&&n!==Mr&&(st=n.relatedTarget||n.fromElement)&&(nu(st)||st[Xl]))break t;if((B||L)&&(L=Y.window===Y?Y:(L=Y.ownerDocument)?L.defaultView||L.parentWindow:window,B?(st=n.relatedTarget||n.toElement,B=U,st=st?nu(st):null,st!==null&&(ee=T(st),ot=st.tag,st!==ee||ot!==5&&ot!==27&&ot!==6)&&(st=null)):(B=null,st=U),B!==st)){if(ot=Vl,Q="onMouseLeave",C="onMouseEnter",E="mouse",(t==="pointerout"||t==="pointerover")&&(ot=xl,Q="onPointerLeave",C="onPointerEnter",E="pointer"),ee=B==null?L:iu(B),R=st==null?L:iu(st),L=new ot(Q,E+"leave",B,n,Y),L.target=ee,L.relatedTarget=R,Q=null,nu(Y)===U&&(ot=new ot(C,E+"enter",st,n,Y),ot.target=R,ot.relatedTarget=ee,Q=ot),ee=Q,B&&st)e:{for(ot=B,C=st,E=0,R=ot;R;R=Me(R))E++;for(R=0,Q=C;Q;Q=Me(Q))R++;for(;0<E-R;)ot=Me(ot),E--;for(;0<R-E;)C=Me(C),R--;for(;E--;){if(ot===C||C!==null&&ot===C.alternate)break e;ot=Me(ot),C=Me(C)}ot=null}else ot=null;B!==null&&Se(j,L,B,ot,!1),st!==null&&ee!==null&&Se(j,ee,st,ot,!0)}}t:{if(L=U?iu(U):window,B=L.nodeName&&L.nodeName.toLowerCase(),B==="select"||B==="input"&&L.type==="file")var lt=Ys;else if(qr(L))if(Xs)lt=hg;else{lt=Jl;var Tt=Vs}else B=L.nodeName,!B||B.toLowerCase()!=="input"||L.type!=="checkbox"&&L.type!=="radio"?U&&kc(U.elementType)&&(lt=Ys):lt=Xr;if(lt&&(lt=lt(t,U))){mu(j,lt,n,Y);break t}Tt&&Tt(t,L,U),t==="focusout"&&U&&L.type==="number"&&U.memoizedProps.value!=null&&ra(L,"number",L.value)}switch(Tt=U?iu(U):window,t){case"focusin":(qr(Tt)||Tt.contentEditable==="true")&&(bu=Tt,Wl=U,D=null);break;case"focusout":D=Wl=bu=null;break;case"mousedown":G=!0;break;case"contextmenu":case"mouseup":case"dragend":G=!1,H(j,n,Y);break;case"selectionchange":if(gg)break;case"keydown":case"keyup":H(j,n,Y)}var rt;if(Lc)t:{switch(t){case"compositionstart":var ht="onCompositionStart";break t;case"compositionend":ht="onCompositionEnd";break t;case"compositionupdate":ht="onCompositionUpdate";break t}ht=void 0}else Kl?qs(t,n)&&(ht="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(ht="onCompositionStart");ht&&(Lr&&n.locale!=="ko"&&(Kl||ht!=="onCompositionStart"?ht==="onCompositionEnd"&&Kl&&(rt=Es()):(Zl=Y,Dc="value"in Zl?Zl.value:Zl.textContent,Kl=!0)),Tt=Hf(U,ht),0<Tt.length&&(ht=new an(ht,t,null,n,Y),j.push({event:ht,listeners:Tt}),rt?ht.data=rt:(rt=Nr(n),rt!==null&&(ht.data=rt)))),(rt=Ns?og(t,n):Gs(t,n))&&(ht=Hf(U,"onBeforeInput"),0<ht.length&&(Tt=new an("onBeforeInput","beforeinput",null,n,Y),j.push({event:Tt,listeners:ht}),Tt.data=rt)),Cg(j,t,U,n,Y)}md(j,e)})}function vt(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Hf(t,e){for(var n=e+"Capture",u=[];t!==null;){var a=t,f=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||f===null||(a=ca(t,n),a!=null&&u.unshift(vt(t,a,f)),a=ca(t,e),a!=null&&u.push(vt(t,a,f))),t.tag===3)return u;t=t.return}return[]}function Me(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Se(t,e,n,u,a){for(var f=e._reactName,h=[];n!==null&&n!==u;){var v=n,A=v.alternate,U=v.stateNode;if(v=v.tag,A!==null&&A===u)break;v!==5&&v!==26&&v!==27||U===null||(A=U,a?(U=ca(n,f),U!=null&&h.unshift(vt(n,U,A))):a||(U=ca(n,f),U!=null&&h.push(vt(n,U,A)))),n=n.return}h.length!==0&&t.push({event:e,listeners:h})}var Bg=/\r\n?/g,gi=/\u0000|\uFFFD/g;function vd(t){return(typeof t=="string"?t:""+t).replace(Bg,`
`).replace(gi,"")}function bd(t,e){return e=vd(e),vd(t)===e}function Nf(){}function te(t,e,n,u,a,f){switch(n){case"children":typeof u=="string"?e==="body"||e==="textarea"&&u===""||Ql(t,u):(typeof u=="number"||typeof u=="bigint")&&e!=="body"&&Ql(t,""+u);break;case"className":Tr(t,"class",u);break;case"tabIndex":Tr(t,"tabindex",u);break;case"dir":case"role":case"viewBox":case"width":case"height":Tr(t,n,u);break;case"style":wc(t,u,f);break;case"data":if(e!=="object"){Tr(t,"data",u);break}case"src":case"href":if(u===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(u==null||typeof u=="function"||typeof u=="symbol"||typeof u=="boolean"){t.removeAttribute(n);break}u=fa(""+u),t.setAttribute(n,u);break;case"action":case"formAction":if(typeof u=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(n==="formAction"?(e!=="input"&&te(t,e,"name",a.name,a,null),te(t,e,"formEncType",a.formEncType,a,null),te(t,e,"formMethod",a.formMethod,a,null),te(t,e,"formTarget",a.formTarget,a,null)):(te(t,e,"encType",a.encType,a,null),te(t,e,"method",a.method,a,null),te(t,e,"target",a.target,a,null)));if(u==null||typeof u=="symbol"||typeof u=="boolean"){t.removeAttribute(n);break}u=fa(""+u),t.setAttribute(n,u);break;case"onClick":u!=null&&(t.onclick=Nf);break;case"onScroll":u!=null&&zt("scroll",t);break;case"onScrollEnd":u!=null&&zt("scrollend",t);break;case"dangerouslySetInnerHTML":if(u!=null){if(typeof u!="object"||!("__html"in u))throw Error(g(61));if(n=u.__html,n!=null){if(a.children!=null)throw Error(g(60));t.innerHTML=n}}break;case"multiple":t.multiple=u&&typeof u!="function"&&typeof u!="symbol";break;case"muted":t.muted=u&&typeof u!="function"&&typeof u!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(u==null||typeof u=="function"||typeof u=="boolean"||typeof u=="symbol"){t.removeAttribute("xlink:href");break}n=fa(""+u),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":u!=null&&typeof u!="function"&&typeof u!="symbol"?t.setAttribute(n,""+u):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":u&&typeof u!="function"&&typeof u!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":u===!0?t.setAttribute(n,""):u!==!1&&u!=null&&typeof u!="function"&&typeof u!="symbol"?t.setAttribute(n,u):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":u!=null&&typeof u!="function"&&typeof u!="symbol"&&!isNaN(u)&&1<=u?t.setAttribute(n,u):t.removeAttribute(n);break;case"rowSpan":case"start":u==null||typeof u=="function"||typeof u=="symbol"||isNaN(u)?t.removeAttribute(n):t.setAttribute(n,u);break;case"popover":zt("beforetoggle",t),zt("toggle",t),Ar(t,"popover",u);break;case"xlinkActuate":rl(t,"http://www.w3.org/1999/xlink","xlink:actuate",u);break;case"xlinkArcrole":rl(t,"http://www.w3.org/1999/xlink","xlink:arcrole",u);break;case"xlinkRole":rl(t,"http://www.w3.org/1999/xlink","xlink:role",u);break;case"xlinkShow":rl(t,"http://www.w3.org/1999/xlink","xlink:show",u);break;case"xlinkTitle":rl(t,"http://www.w3.org/1999/xlink","xlink:title",u);break;case"xlinkType":rl(t,"http://www.w3.org/1999/xlink","xlink:type",u);break;case"xmlBase":rl(t,"http://www.w3.org/XML/1998/namespace","xml:base",u);break;case"xmlLang":rl(t,"http://www.w3.org/XML/1998/namespace","xml:lang",u);break;case"xmlSpace":rl(t,"http://www.w3.org/XML/1998/namespace","xml:space",u);break;case"is":Ar(t,"is",u);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=ws.get(n)||n,Ar(t,n,u))}}function Jo(t,e,n,u,a,f){switch(n){case"style":wc(t,u,f);break;case"dangerouslySetInnerHTML":if(u!=null){if(typeof u!="object"||!("__html"in u))throw Error(g(61));if(n=u.__html,n!=null){if(a.children!=null)throw Error(g(60));t.innerHTML=n}}break;case"children":typeof u=="string"?Ql(t,u):(typeof u=="number"||typeof u=="bigint")&&Ql(t,""+u);break;case"onScroll":u!=null&&zt("scroll",t);break;case"onScrollEnd":u!=null&&zt("scrollend",t);break;case"onClick":u!=null&&(t.onclick=Nf);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!xs.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(a=n.endsWith("Capture"),e=n.slice(2,a?n.length-7:void 0),f=t[ln]||null,f=f!=null?f[n]:null,typeof f=="function"&&t.removeEventListener(e,f,a),typeof u=="function")){typeof f!="function"&&f!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,u,a);break t}n in t?t[n]=u:u===!0?t.setAttribute(n,""):Ar(t,n,u)}}}function De(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":zt("error",t),zt("load",t);var u=!1,a=!1,f;for(f in n)if(n.hasOwnProperty(f)){var h=n[f];if(h!=null)switch(f){case"src":u=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(g(137,e));default:te(t,e,f,h,n,null)}}a&&te(t,e,"srcSet",n.srcSet,n,null),u&&te(t,e,"src",n.src,n,null);return;case"input":zt("invalid",t);var v=f=h=a=null,A=null,U=null;for(u in n)if(n.hasOwnProperty(u)){var Y=n[u];if(Y!=null)switch(u){case"name":a=Y;break;case"type":h=Y;break;case"checked":A=Y;break;case"defaultChecked":U=Y;break;case"value":f=Y;break;case"defaultValue":v=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(g(137,e));break;default:te(t,e,u,Y,n,null)}}xc(t,f,v,A,U,h,a,!1),kr(t);return;case"select":zt("invalid",t),u=h=f=null;for(a in n)if(n.hasOwnProperty(a)&&(v=n[a],v!=null))switch(a){case"value":f=v;break;case"defaultValue":h=v;break;case"multiple":u=v;default:te(t,e,a,v,n,null)}e=f,n=h,t.multiple=!!u,e!=null?fu(t,!!u,e,!1):n!=null&&fu(t,!!u,n,!0);return;case"textarea":zt("invalid",t),f=a=u=null;for(h in n)if(n.hasOwnProperty(h)&&(v=n[h],v!=null))switch(h){case"value":u=v;break;case"defaultValue":a=v;break;case"children":f=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(g(91));break;default:te(t,e,h,v,n,null)}Er(t,u,a,f),kr(t);return;case"option":for(A in n)if(n.hasOwnProperty(A)&&(u=n[A],u!=null))switch(A){case"selected":t.selected=u&&typeof u!="function"&&typeof u!="symbol";break;default:te(t,e,A,u,n,null)}return;case"dialog":zt("beforetoggle",t),zt("toggle",t),zt("cancel",t),zt("close",t);break;case"iframe":case"object":zt("load",t);break;case"video":case"audio":for(u=0;u<rr.length;u++)zt(rr[u],t);break;case"image":zt("error",t),zt("load",t);break;case"details":zt("toggle",t);break;case"embed":case"source":case"link":zt("error",t),zt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(U in n)if(n.hasOwnProperty(U)&&(u=n[U],u!=null))switch(U){case"children":case"dangerouslySetInnerHTML":throw Error(g(137,e));default:te(t,e,U,u,n,null)}return;default:if(kc(e)){for(Y in n)n.hasOwnProperty(Y)&&(u=n[Y],u!==void 0&&Jo(t,e,Y,u,n,void 0));return}}for(v in n)n.hasOwnProperty(v)&&(u=n[v],u!=null&&te(t,e,v,u,n,null))}function Lg(t,e,n,u){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,f=null,h=null,v=null,A=null,U=null,Y=null;for(B in n){var j=n[B];if(n.hasOwnProperty(B)&&j!=null)switch(B){case"checked":break;case"value":break;case"defaultValue":A=j;default:u.hasOwnProperty(B)||te(t,e,B,null,u,j)}}for(var L in u){var B=u[L];if(j=n[L],u.hasOwnProperty(L)&&(B!=null||j!=null))switch(L){case"type":f=B;break;case"name":a=B;break;case"checked":U=B;break;case"defaultChecked":Y=B;break;case"value":h=B;break;case"defaultValue":v=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(g(137,e));break;default:B!==j&&te(t,e,L,B,u,j)}}zr(t,h,v,A,U,Y,f,a);return;case"select":B=h=v=L=null;for(f in n)if(A=n[f],n.hasOwnProperty(f)&&A!=null)switch(f){case"value":break;case"multiple":B=A;default:u.hasOwnProperty(f)||te(t,e,f,null,u,A)}for(a in u)if(f=u[a],A=n[a],u.hasOwnProperty(a)&&(f!=null||A!=null))switch(a){case"value":L=f;break;case"defaultValue":v=f;break;case"multiple":h=f;default:f!==A&&te(t,e,a,f,u,A)}e=v,n=h,u=B,L!=null?fu(t,!!n,L,!1):!!u!=!!n&&(e!=null?fu(t,!!n,e,!0):fu(t,!!n,n?[]:"",!1));return;case"textarea":B=L=null;for(v in n)if(a=n[v],n.hasOwnProperty(v)&&a!=null&&!u.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:te(t,e,v,null,u,a)}for(h in u)if(a=u[h],f=n[h],u.hasOwnProperty(h)&&(a!=null||f!=null))switch(h){case"value":L=a;break;case"defaultValue":B=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(g(91));break;default:a!==f&&te(t,e,h,a,u,f)}Ac(t,L,B);return;case"option":for(var st in n)if(L=n[st],n.hasOwnProperty(st)&&L!=null&&!u.hasOwnProperty(st))switch(st){case"selected":t.selected=!1;break;default:te(t,e,st,null,u,L)}for(A in u)if(L=u[A],B=n[A],u.hasOwnProperty(A)&&L!==B&&(L!=null||B!=null))switch(A){case"selected":t.selected=L&&typeof L!="function"&&typeof L!="symbol";break;default:te(t,e,A,L,u,B)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ot in n)L=n[ot],n.hasOwnProperty(ot)&&L!=null&&!u.hasOwnProperty(ot)&&te(t,e,ot,null,u,L);for(U in u)if(L=u[U],B=n[U],u.hasOwnProperty(U)&&L!==B&&(L!=null||B!=null))switch(U){case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(g(137,e));break;default:te(t,e,U,L,u,B)}return;default:if(kc(e)){for(var ee in n)L=n[ee],n.hasOwnProperty(ee)&&L!==void 0&&!u.hasOwnProperty(ee)&&Jo(t,e,ee,void 0,u,L);for(Y in u)L=u[Y],B=n[Y],!u.hasOwnProperty(Y)||L===B||L===void 0&&B===void 0||Jo(t,e,Y,L,u,B);return}}for(var C in n)L=n[C],n.hasOwnProperty(C)&&L!=null&&!u.hasOwnProperty(C)&&te(t,e,C,null,u,L);for(j in u)L=u[j],B=n[j],!u.hasOwnProperty(j)||L===B||L==null&&B==null||te(t,e,j,L,u,B)}var tl=null,qf=null;function ji(t){return t.nodeType===9?t:t.ownerDocument}function me(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function pe(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Gf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Wo=null;function Hg(){var t=window.event;return t&&t.type==="popstate"?t===Wo?!1:(Wo=t,!0):(Wo=null,!1)}var yd=typeof setTimeout=="function"?setTimeout:void 0,Ng=typeof clearTimeout=="function"?clearTimeout:void 0,Sd=typeof Promise=="function"?Promise:void 0,qg=typeof queueMicrotask=="function"?queueMicrotask:typeof Sd<"u"?function(t){return Sd.resolve(null).then(t).catch(_d)}:yd;function _d(t){setTimeout(function(){throw t})}function Vn(t){return t==="head"}function Yf(t,e){var n=e,u=0,a=0;do{var f=n.nextSibling;if(t.removeChild(n),f&&f.nodeType===8)if(n=f.data,n==="/$"){if(0<u&&8>u){n=u;var h=t.ownerDocument;if(n&1&&fr(h.documentElement),n&2&&fr(h.body),n&4)for(n=h.head,fr(n),h=n.firstChild;h;){var v=h.nextSibling,A=h.nodeName;h[ua]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=v}}if(a===0){t.removeChild(f),pr(e);return}a--}else n==="$"||n==="$?"||n==="$!"?a++:u=n.charCodeAt(0)-48;else u=0;n=f}while(n);pr(e)}function Fo(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Fo(n),gc(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function xd(t,e,n,u){for(;t.nodeType===1;){var a=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!u&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(u){if(!t[ua])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=el(t.nextSibling),t===null)break}return null}function Xf(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=el(t.nextSibling),t===null))return null;return t}function tn(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Ku(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var u=function(){e(),n.removeEventListener("DOMContentLoaded",u)};n.addEventListener("DOMContentLoaded",u),t._reactRetry=u}}function el(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var $o=null;function Ad(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Td(t,e,n){switch(e=ji(n),t){case"html":if(t=e.documentElement,!t)throw Error(g(452));return t;case"head":if(t=e.head,!t)throw Error(g(453));return t;case"body":if(t=e.body,!t)throw Error(g(454));return t;default:throw Error(g(451))}}function fr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);gc(t)}var Dn=new Map,bl=new Set;function yt(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var yl=$.d;$.d={f:Kn,r:wd,D:Gg,C:kd,L:Qf,m:Yg,X:Qg,S:Xg,M:Zg};function Kn(){var t=yl.f(),e=Df();return t||e}function wd(t){var e=lu(t);e!==null&&e.tag===5&&e.type==="form"?df(e):yl.r(t)}var ju=typeof document>"u"?null:document;function Xt(t,e,n){var u=ju;if(u&&typeof e=="string"&&e){var a=On(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof n=="string"&&(a+='[crossorigin="'+n+'"]'),bl.has(a)||(bl.add(a),t={rel:t,crossOrigin:n,href:e},u.querySelector(a)===null&&(e=u.createElement("link"),De(e,"link",t),He(e),u.head.appendChild(e)))}}function Gg(t){yl.D(t),Xt("dns-prefetch",t,null)}function kd(t,e){yl.C(t,e),Xt("preconnect",t,e)}function Qf(t,e,n){yl.L(t,e,n);var u=ju;if(u&&t&&e){var a='link[rel="preload"][as="'+On(e)+'"]';e==="image"&&n&&n.imageSrcSet?(a+='[imagesrcset="'+On(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(a+='[imagesizes="'+On(n.imageSizes)+'"]')):a+='[href="'+On(t)+'"]';var f=a;switch(e){case"style":f=Ju(t);break;case"script":f=Wu(t)}Dn.has(f)||(t=q({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Dn.set(f,t),u.querySelector(a)!==null||e==="style"&&u.querySelector(cr(f))||e==="script"&&u.querySelector(or(f))||(e=u.createElement("link"),De(e,"link",t),He(e),u.head.appendChild(e)))}}function Yg(t,e){yl.m(t,e);var n=ju;if(n&&t){var u=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+On(u)+'"][href="'+On(t)+'"]',f=a;switch(u){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=Wu(t)}if(!Dn.has(f)&&(t=q({rel:"modulepreload",href:t},e),Dn.set(f,t),n.querySelector(a)===null)){switch(u){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(or(f)))return}u=n.createElement("link"),De(u,"link",t),He(u),n.head.appendChild(u)}}}function Xg(t,e,n){yl.S(t,e,n);var u=ju;if(u&&t){var a=uu(u).hoistableStyles,f=Ju(t);e=e||"default";var h=a.get(f);if(!h){var v={loading:0,preload:null};if(h=u.querySelector(cr(f)))v.loading=5;else{t=q({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Dn.get(f))&&sr(t,n);var A=h=u.createElement("link");He(A),De(A,"link",t),A._p=new Promise(function(U,Y){A.onload=U,A.onerror=Y}),A.addEventListener("load",function(){v.loading|=1}),A.addEventListener("error",function(){v.loading|=2}),v.loading|=4,Zf(h,e,u)}h={type:"stylesheet",instance:h,count:1,state:v},a.set(f,h)}}}function Qg(t,e){yl.X(t,e);var n=ju;if(n&&t){var u=uu(n).hoistableScripts,a=Wu(t),f=u.get(a);f||(f=n.querySelector(or(a)),f||(t=q({src:t,async:!0},e),(e=Dn.get(a))&&Io(t,e),f=n.createElement("script"),He(f),De(f,"link",t),n.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},u.set(a,f))}}function Zg(t,e){yl.M(t,e);var n=ju;if(n&&t){var u=uu(n).hoistableScripts,a=Wu(t),f=u.get(a);f||(f=n.querySelector(or(a)),f||(t=q({src:t,async:!0,type:"module"},e),(e=Dn.get(a))&&Io(t,e),f=n.createElement("script"),He(f),De(f,"link",t),n.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},u.set(a,f))}}function zd(t,e,n,u){var a=(a=we.current)?yt(a):null;if(!a)throw Error(g(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ju(n.href),n=uu(a).hoistableStyles,u=n.get(e),u||(u={type:"style",instance:null,count:0,state:null},n.set(e,u)),u):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ju(n.href);var f=uu(a).hoistableStyles,h=f.get(t);if(h||(a=a.ownerDocument||a,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,h),(f=a.querySelector(cr(t)))&&!f._p&&(h.instance=f,h.state.loading=5),Dn.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Dn.set(t,n),f||Vg(a,t,n,h.state))),e&&u===null)throw Error(g(528,""));return h}if(e&&u!==null)throw Error(g(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Wu(n),n=uu(a).hoistableScripts,u=n.get(e),u||(u={type:"script",instance:null,count:0,state:null},n.set(e,u)),u):{type:"void",instance:null,count:0,state:null};default:throw Error(g(444,t))}}function Ju(t){return'href="'+On(t)+'"'}function cr(t){return'link[rel="stylesheet"]['+t+"]"}function Ed(t){return q({},t,{"data-precedence":t.precedence,precedence:null})}function Vg(t,e,n,u){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?u.loading=1:(e=t.createElement("link"),u.preload=e,e.addEventListener("load",function(){return u.loading|=1}),e.addEventListener("error",function(){return u.loading|=2}),De(e,"link",n),He(e),t.head.appendChild(e))}function Wu(t){return'[src="'+On(t)+'"]'}function or(t){return"script[async]"+t}function Md(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var u=t.querySelector('style[data-href~="'+On(n.href)+'"]');if(u)return e.instance=u,He(u),u;var a=q({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return u=(t.ownerDocument||t).createElement("style"),He(u),De(u,"style",a),Zf(u,n.precedence,t),e.instance=u;case"stylesheet":a=Ju(n.href);var f=t.querySelector(cr(a));if(f)return e.state.loading|=4,e.instance=f,He(f),f;u=Ed(n),(a=Dn.get(a))&&sr(u,a),f=(t.ownerDocument||t).createElement("link"),He(f);var h=f;return h._p=new Promise(function(v,A){h.onload=v,h.onerror=A}),De(f,"link",u),e.state.loading|=4,Zf(f,n.precedence,t),e.instance=f;case"script":return f=Wu(n.src),(a=t.querySelector(or(f)))?(e.instance=a,He(a),a):(u=n,(a=Dn.get(f))&&(u=q({},n),Io(u,a)),t=t.ownerDocument||t,a=t.createElement("script"),He(a),De(a,"link",u),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(g(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(u=e.instance,e.state.loading|=4,Zf(u,n.precedence,t));return e.instance}function Zf(t,e,n){for(var u=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=u.length?u[u.length-1]:null,f=a,h=0;h<u.length;h++){var v=u[h];if(v.dataset.precedence===e)f=v;else if(f!==a)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function sr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Io(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Fu=null;function Dd(t,e,n){if(Fu===null){var u=new Map,a=Fu=new Map;a.set(n,u)}else a=Fu,u=a.get(n),u||(u=new Map,a.set(n,u));if(u.has(t))return u;for(u.set(t,null),n=n.getElementsByTagName(t),a=0;a<n.length;a++){var f=n[a];if(!(f[ua]||f[Le]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var h=f.getAttribute(e)||"";h=t+h;var v=u.get(h);v?v.push(f):u.set(h,[f])}}return u}function Cd(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Kg(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ce(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ge=null;function jg(){}function Jg(t,e,n){if(Ge===null)throw Error(g(475));var u=Ge;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var a=Ju(n.href),f=t.querySelector(cr(a));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(u.count++,u=hr.bind(u),t.then(u,u)),e.state.loading|=4,e.instance=f,He(f);return}f=t.ownerDocument||t,n=Ed(n),(a=Dn.get(a))&&sr(n,a),f=f.createElement("link"),He(f);var h=f;h._p=new Promise(function(v,A){h.onload=v,h.onerror=A}),De(f,"link",n),e.instance=f}u.stylesheets===null&&(u.stylesheets=new Map),u.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(u.count++,e=hr.bind(u),t.addEventListener("load",e),t.addEventListener("error",e))}}function Wg(){if(Ge===null)throw Error(g(475));var t=Ge;return t.stylesheets&&t.count===0&&Po(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Po(t,t.stylesheets),t.unsuspend){var u=t.unsuspend;t.unsuspend=null,u()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function hr(){if(this.count--,this.count===0){if(this.stylesheets)Po(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Vf=null;function Po(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Vf=new Map,e.forEach(Fg,t),Vf=null,hr.call(t))}function Fg(t,e){if(!(e.state.loading&4)){var n=Vf.get(t);if(n)var u=n.get(null);else{n=new Map,Vf.set(t,n);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<a.length;f++){var h=a[f];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),u=h)}u&&n.set(null,u)}a=e.instance,h=a.getAttribute("data-precedence"),f=n.get(h)||u,f===u&&n.set(null,a),n.set(h,a),this.count++,u=hr.bind(this),a.addEventListener("load",u),a.addEventListener("error",u),f?f.parentNode.insertBefore(a,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var $u={$$typeof:it,Provider:null,Consumer:null,_currentValue:ft,_currentValue2:ft,_threadCount:0};function $g(t,e,n,u,a,f,h,v){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Sr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Sr(0),this.hiddenUpdates=Sr(null),this.identifierPrefix=u,this.onUncaughtError=a,this.onCaughtError=f,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function Od(t,e,n,u,a,f,h,v,A,U,Y,j){return t=new $g(t,e,n,h,v,A,U,j),e=1,f===!0&&(e|=24),f=rn(3,null,null,e),t.current=f,f.stateNode=t,e=Kc(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:u,isDehydrated:n,cache:e},Ui(f),t}function Rd(t){return t?(t=zi,t):zi}function ts(t,e,n,u,a,f){a=Rd(a),u.context===null?u.context=a:u.pendingContext=a,u=Pl(e),u.payload={element:n},f=f===void 0?null:f,f!==null&&(u.callback=f),n=kl(t,u,e),n!==null&&(Mn(n,t,e),wa(n,t,e))}function es(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function ns(t,e){es(t,e),(t=t.alternate)&&es(t,e)}function Ud(t){if(t.tag===13){var e=ki(t,67108864);e!==null&&Mn(e,t,67108864),ns(t,67108864)}}var Kf=!0;function Ig(t,e,n,u){var a=X.T;X.T=null;var f=$.p;try{$.p=2,ls(t,e,n,u)}finally{$.p=f,X.T=a}}function Iu(t,e,n,u){var a=X.T;X.T=null;var f=$.p;try{$.p=8,ls(t,e,n,u)}finally{$.p=f,X.T=a}}function ls(t,e,n,u){if(Kf){var a=is(u);if(a===null)Hl(t,e,u,jf,n),Ld(t,u);else if(t0(a,t,e,n,u))u.stopPropagation();else if(Ld(t,u),e&4&&-1<Pg.indexOf(t)){for(;a!==null;){var f=lu(a);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var h=Sl(f.pendingLanes);if(h!==0){var v=f;for(v.pendingLanes|=2,v.entangledLanes|=2;h;){var A=1<<31-Je(h);v.entanglements[1]|=A,h&=~A}vl(f),(Jt&6)===0&&(Gu=Be()+500,ir(0))}}break;case 13:v=ki(f,2),v!==null&&Mn(v,f,2),Df(),ns(f,2)}if(f=is(u),f===null&&Hl(t,e,u,jf,n),f===a)break;a=f}a!==null&&u.stopPropagation()}else Hl(t,e,u,null,n)}}function is(t){return t=zc(t),us(t)}var jf=null;function us(t){if(jf=null,t=nu(t),t!==null){var e=T(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=y(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return jf=t,null}function Bd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ds()){case gs:return 2;case $i:return 8;case nn:case ea:return 32;case na:return 268435456;default:return 32}default:return 32}}var Jf=!1,Nl=null,mi=null,pi=null,dr=new Map,gr=new Map,vi=[],Pg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Ld(t,e){switch(t){case"focusin":case"focusout":Nl=null;break;case"dragenter":case"dragleave":mi=null;break;case"mouseover":case"mouseout":pi=null;break;case"pointerover":case"pointerout":dr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":gr.delete(e.pointerId)}}function mr(t,e,n,u,a,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:n,eventSystemFlags:u,nativeEvent:f,targetContainers:[a]},e!==null&&(e=lu(e),e!==null&&Ud(e)),t):(t.eventSystemFlags|=u,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function t0(t,e,n,u,a){switch(e){case"focusin":return Nl=mr(Nl,t,e,n,u,a),!0;case"dragenter":return mi=mr(mi,t,e,n,u,a),!0;case"mouseover":return pi=mr(pi,t,e,n,u,a),!0;case"pointerover":var f=a.pointerId;return dr.set(f,mr(dr.get(f)||null,t,e,n,u,a)),!0;case"gotpointercapture":return f=a.pointerId,gr.set(f,mr(gr.get(f)||null,t,e,n,u,a)),!0}return!1}function Hd(t){var e=nu(t.target);if(e!==null){var n=T(e);if(n!==null){if(e=n.tag,e===13){if(e=y(n),e!==null){t.blockedOn=e,Id(t.priority,function(){if(n.tag===13){var u=je();u=hc(u);var a=ki(n,u);a!==null&&Mn(a,n,u),ns(n,u)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Wf(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=is(t.nativeEvent);if(n===null){n=t.nativeEvent;var u=new n.constructor(n.type,n);Mr=u,n.target.dispatchEvent(u),Mr=null}else return e=lu(n),e!==null&&Ud(e),t.blockedOn=n,!1;e.shift()}return!0}function Nd(t,e,n){Wf(t)&&n.delete(e)}function e0(){Jf=!1,Nl!==null&&Wf(Nl)&&(Nl=null),mi!==null&&Wf(mi)&&(mi=null),pi!==null&&Wf(pi)&&(pi=null),dr.forEach(Nd),gr.forEach(Nd)}function Ff(t,e){t.blockedOn===e&&(t.blockedOn=null,Jf||(Jf=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,e0)))}var $f=null;function qd(t){$f!==t&&($f=t,o.unstable_scheduleCallback(o.unstable_NormalPriority,function(){$f===t&&($f=null);for(var e=0;e<t.length;e+=3){var n=t[e],u=t[e+1],a=t[e+2];if(typeof u!="function"){if(us(u||n)===null)continue;break}var f=lu(n);f!==null&&(t.splice(e,3),e-=3,La(f,{pending:!0,data:a,method:n.method,action:u},u,a))}}))}function pr(t){function e(A){return Ff(A,t)}Nl!==null&&Ff(Nl,t),mi!==null&&Ff(mi,t),pi!==null&&Ff(pi,t),dr.forEach(e),gr.forEach(e);for(var n=0;n<vi.length;n++){var u=vi[n];u.blockedOn===t&&(u.blockedOn=null)}for(;0<vi.length&&(n=vi[0],n.blockedOn===null);)Hd(n),n.blockedOn===null&&vi.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(u=0;u<n.length;u+=3){var a=n[u],f=n[u+1],h=a[ln]||null;if(typeof f=="function")h||qd(n);else if(h){var v=null;if(f&&f.hasAttribute("formAction")){if(a=f,h=f[ln]||null)v=h.formAction;else if(us(a)!==null)continue}else v=h.action;typeof v=="function"?n[u+1]=v:(n.splice(u,3),u-=3),qd(n)}}}function as(t){this._internalRoot=t}If.prototype.render=as.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(g(409));var n=e.current,u=je();ts(n,u,t,e,null,null)},If.prototype.unmount=as.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;ts(t.current,2,null,t,null,null),Df(),e[Xl]=null}};function If(t){this._internalRoot=t}If.prototype.unstable_scheduleHydration=function(t){if(t){var e=ys();t={blockedOn:null,target:t,priority:e};for(var n=0;n<vi.length&&e!==0&&e<vi[n].priority;n++);vi.splice(n,0,t),n===0&&Hd(t)}};var Gd=m.version;if(Gd!=="19.1.0")throw Error(g(527,Gd,"19.1.0"));$.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(g(188)):(t=Object.keys(t).join(","),Error(g(268,t)));return t=x(e),t=t!==null?w(t):null,t=t===null?null:t.stateNode,t};var n0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:X,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Pf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Pf.isDisabled&&Pf.supportsFiber)try{ia=Pf.inject(n0),Xe=Pf}catch{}}return rs.createRoot=function(t,e){if(!S(t))throw Error(g(299));var n=!1,u="",a=yo,f=pf,h=wh,v=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(u=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(h=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(v=e.unstable_transitionCallbacks)),e=Od(t,1,!1,null,null,n,u,a,f,h,v,null),t[Xl]=e.current,jo(t),new as(e)},rs.hydrateRoot=function(t,e,n){if(!S(t))throw Error(g(299));var u=!1,a="",f=yo,h=pf,v=wh,A=null,U=null;return n!=null&&(n.unstable_strictMode===!0&&(u=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onUncaughtError!==void 0&&(f=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(v=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(A=n.unstable_transitionCallbacks),n.formState!==void 0&&(U=n.formState)),e=Od(t,1,!0,e,n??null,u,a,f,h,v,A,U),e.context=Rd(null),n=e.current,u=je(),u=hc(u),a=Pl(u),a.callback=null,kl(n,a,u),n=u,e.current.lanes=n,eu(e,n),vl(e),t[Xl]=e.current,jo(t),new If(e)},rs.version="19.1.0",rs}var z0;function am(){if(z0)return r0.exports;z0=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(m){console.error(m)}}return o(),r0.exports=um(),r0.exports}var rm=am();function E0(o,m){const c=String(o);if(typeof m!="string")throw new TypeError("Expected character");let g=0,S=c.indexOf(m);for(;S!==-1;)g++,S=c.indexOf(m,S+m.length);return g}function fm(o){if(typeof o!="string")throw new TypeError("Expected a string");return o.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function cm(o,m,c){const S=G0((c||{}).ignore||[]),T=om(m);let y=-1;for(;++y<T.length;)Q1(o,"text",z);function z(w,q){let M=-1,N;for(;++M<q.length;){const F=q[M],Z=N?N.children:void 0;if(S(F,Z?Z.indexOf(F):void 0,N))return;N=F}if(N)return x(w,q)}function x(w,q){const M=q[q.length-1],N=T[y][0],F=T[y][1];let Z=0;const et=M.children.indexOf(w);let ut=!1,dt=[];N.lastIndex=0;let it=N.exec(w.value);for(;it;){const Ut=it.index,Ct={index:it.index,input:it.input,stack:[...q,w]};let Lt=F(...it,Ct);if(typeof Lt=="string"&&(Lt=Lt.length>0?{type:"text",value:Lt}:void 0),Lt===!1?N.lastIndex=Ut+1:(Z!==Ut&&dt.push({type:"text",value:w.value.slice(Z,Ut)}),Array.isArray(Lt)?dt.push(...Lt):Lt&&dt.push(Lt),Z=Ut+it[0].length,ut=!0),!N.global)break;it=N.exec(w.value)}return ut?(Z<w.value.length&&dt.push({type:"text",value:w.value.slice(Z)}),M.children.splice(et,1,...dt)):dt=[w],et+dt.length}}function om(o){const m=[];if(!Array.isArray(o))throw new TypeError("Expected find and replace tuple or list of tuples");const c=!o[0]||Array.isArray(o[0])?o:[o];let g=-1;for(;++g<c.length;){const S=c[g];m.push([sm(S[0]),hm(S[1])])}return m}function sm(o){return typeof o=="string"?new RegExp(fm(o),"g"):o}function hm(o){return typeof o=="function"?o:function(){return o}}const o0="phrasing",s0=["autolink","link","image","label"];function dm(){return{transforms:[Sm],enter:{literalAutolink:mm,literalAutolinkEmail:h0,literalAutolinkHttp:h0,literalAutolinkWww:h0},exit:{literalAutolink:ym,literalAutolinkEmail:bm,literalAutolinkHttp:pm,literalAutolinkWww:vm}}}function gm(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:o0,notInConstruct:s0},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:o0,notInConstruct:s0},{character:":",before:"[ps]",after:"\\/",inConstruct:o0,notInConstruct:s0}]}}function mm(o){this.enter({type:"link",title:null,url:"",children:[]},o)}function h0(o){this.config.enter.autolinkProtocol.call(this,o)}function pm(o){this.config.exit.autolinkProtocol.call(this,o)}function vm(o){this.config.exit.data.call(this,o);const m=this.stack[this.stack.length-1];rc(m.type==="link"),m.url="http://"+this.sliceSerialize(o)}function bm(o){this.config.exit.autolinkEmail.call(this,o)}function ym(o){this.exit(o)}function Sm(o){cm(o,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,_m],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),xm]],{ignore:["link","linkReference"]})}function _m(o,m,c,g,S){let T="";if(!Q0(S)||(/^w/i.test(m)&&(c=m+c,m="",T="http://"),!Am(c)))return!1;const y=Tm(c+g);if(!y[0])return!1;const z={type:"link",title:null,url:T+m+y[0],children:[{type:"text",value:m+y[0]}]};return y[1]?[z,{type:"text",value:y[1]}]:z}function xm(o,m,c,g){return!Q0(g,!0)||/[-\d_]$/.test(c)?!1:{type:"link",title:null,url:"mailto:"+m+"@"+c,children:[{type:"text",value:m+"@"+c}]}}function Am(o){const m=o.split(".");return!(m.length<2||m[m.length-1]&&(/_/.test(m[m.length-1])||!/[a-zA-Z\d]/.test(m[m.length-1]))||m[m.length-2]&&(/_/.test(m[m.length-2])||!/[a-zA-Z\d]/.test(m[m.length-2])))}function Tm(o){const m=/[!"&'),.:;<>?\]}]+$/.exec(o);if(!m)return[o,void 0];o=o.slice(0,m.index);let c=m[0],g=c.indexOf(")");const S=E0(o,"(");let T=E0(o,")");for(;g!==-1&&S>T;)o+=c.slice(0,g+1),c=c.slice(g+1),g=c.indexOf(")"),T++;return[o,c]}function Q0(o,m){const c=o.input.charCodeAt(o.index-1);return(o.index===0||ac(c)||b0(c))&&(!m||c!==47)}Z0.peek=Rm;function wm(){this.buffer()}function km(o){this.enter({type:"footnoteReference",identifier:"",label:""},o)}function zm(){this.buffer()}function Em(o){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},o)}function Mm(o){const m=this.resume(),c=this.stack[this.stack.length-1];rc(c.type==="footnoteReference"),c.identifier=hs(this.sliceSerialize(o)).toLowerCase(),c.label=m}function Dm(o){this.exit(o)}function Cm(o){const m=this.resume(),c=this.stack[this.stack.length-1];rc(c.type==="footnoteDefinition"),c.identifier=hs(this.sliceSerialize(o)).toLowerCase(),c.label=m}function Om(o){this.exit(o)}function Rm(){return"["}function Z0(o,m,c,g){const S=c.createTracker(g);let T=S.move("[^");const y=c.enter("footnoteReference"),z=c.enter("reference");return T+=S.move(c.safe(c.associationId(o),{after:"]",before:T})),z(),y(),T+=S.move("]"),T}function Um(){return{enter:{gfmFootnoteCallString:wm,gfmFootnoteCall:km,gfmFootnoteDefinitionLabelString:zm,gfmFootnoteDefinition:Em},exit:{gfmFootnoteCallString:Mm,gfmFootnoteCall:Dm,gfmFootnoteDefinitionLabelString:Cm,gfmFootnoteDefinition:Om}}}function Bm(o){let m=!1;return o&&o.firstLineBlank&&(m=!0),{handlers:{footnoteDefinition:c,footnoteReference:Z0},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function c(g,S,T,y){const z=T.createTracker(y);let x=z.move("[^");const w=T.enter("footnoteDefinition"),q=T.enter("label");return x+=z.move(T.safe(T.associationId(g),{before:x,after:"]"})),q(),x+=z.move("]:"),g.children&&g.children.length>0&&(z.shift(4),x+=z.move((m?`
`:" ")+T.indentLines(T.containerFlow(g,z.current()),m?V0:Lm))),w(),x}}function Lm(o,m,c){return m===0?o:V0(o,m,c)}function V0(o,m,c){return(c?"":"    ")+o}const Hm=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];K0.peek=Xm;function Nm(){return{canContainEols:["delete"],enter:{strikethrough:Gm},exit:{strikethrough:Ym}}}function qm(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:Hm}],handlers:{delete:K0}}}function Gm(o){this.enter({type:"delete",children:[]},o)}function Ym(o){this.exit(o)}function K0(o,m,c,g){const S=c.createTracker(g),T=c.enter("strikethrough");let y=S.move("~~");return y+=c.containerPhrasing(o,{...S.current(),before:y,after:"~"}),y+=S.move("~~"),T(),y}function Xm(){return"~"}function Qm(o){return o.length}function Zm(o,m){const c=m||{},g=(c.align||[]).concat(),S=c.stringLength||Qm,T=[],y=[],z=[],x=[];let w=0,q=-1;for(;++q<o.length;){const I=[],et=[];let ut=-1;for(o[q].length>w&&(w=o[q].length);++ut<o[q].length;){const dt=Vm(o[q][ut]);if(c.alignDelimiters!==!1){const it=S(dt);et[ut]=it,(x[ut]===void 0||it>x[ut])&&(x[ut]=it)}I.push(dt)}y[q]=I,z[q]=et}let M=-1;if(typeof g=="object"&&"length"in g)for(;++M<w;)T[M]=M0(g[M]);else{const I=M0(g);for(;++M<w;)T[M]=I}M=-1;const N=[],F=[];for(;++M<w;){const I=T[M];let et="",ut="";I===99?(et=":",ut=":"):I===108?et=":":I===114&&(ut=":");let dt=c.alignDelimiters===!1?1:Math.max(1,x[M]-et.length-ut.length);const it=et+"-".repeat(dt)+ut;c.alignDelimiters!==!1&&(dt=et.length+dt+ut.length,dt>x[M]&&(x[M]=dt),F[M]=dt),N[M]=it}y.splice(1,0,N),z.splice(1,0,F),q=-1;const Z=[];for(;++q<y.length;){const I=y[q],et=z[q];M=-1;const ut=[];for(;++M<w;){const dt=I[M]||"";let it="",Ut="";if(c.alignDelimiters!==!1){const Ct=x[M]-(et[M]||0),Lt=T[M];Lt===114?it=" ".repeat(Ct):Lt===99?Ct%2?(it=" ".repeat(Ct/2+.5),Ut=" ".repeat(Ct/2-.5)):(it=" ".repeat(Ct/2),Ut=it):Ut=" ".repeat(Ct)}c.delimiterStart!==!1&&!M&&ut.push("|"),c.padding!==!1&&!(c.alignDelimiters===!1&&dt==="")&&(c.delimiterStart!==!1||M)&&ut.push(" "),c.alignDelimiters!==!1&&ut.push(it),ut.push(dt),c.alignDelimiters!==!1&&ut.push(Ut),c.padding!==!1&&ut.push(" "),(c.delimiterEnd!==!1||M!==w-1)&&ut.push("|")}Z.push(c.delimiterEnd===!1?ut.join("").replace(/ +$/,""):ut.join(""))}return Z.join(`
`)}function Vm(o){return o==null?"":String(o)}function M0(o){const m=typeof o=="string"?o.codePointAt(0):0;return m===67||m===99?99:m===76||m===108?108:m===82||m===114?114:0}function Km(o,m,c,g){const S=c.enter("blockquote"),T=c.createTracker(g);T.move("> "),T.shift(2);const y=c.indentLines(c.containerFlow(o,T.current()),jm);return S(),y}function jm(o,m,c){return">"+(c?"":" ")+o}function Jm(o,m){return D0(o,m.inConstruct,!0)&&!D0(o,m.notInConstruct,!1)}function D0(o,m,c){if(typeof m=="string"&&(m=[m]),!m||m.length===0)return c;let g=-1;for(;++g<m.length;)if(o.includes(m[g]))return!0;return!1}function C0(o,m,c,g){let S=-1;for(;++S<c.unsafe.length;)if(c.unsafe[S].character===`
`&&Jm(c.stack,c.unsafe[S]))return/[ \t]/.test(g.before)?"":" ";return`\\
`}function Wm(o,m){return!!(m.options.fences===!1&&o.value&&!o.lang&&/[^ \r\n]/.test(o.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(o.value))}function Fm(o){const m=o.options.fence||"`";if(m!=="`"&&m!=="~")throw new Error("Cannot serialize code with `"+m+"` for `options.fence`, expected `` ` `` or `~`");return m}function $m(o,m,c,g){const S=Fm(c),T=o.value||"",y=S==="`"?"GraveAccent":"Tilde";if(Wm(o,c)){const M=c.enter("codeIndented"),N=c.indentLines(T,Im);return M(),N}const z=c.createTracker(g),x=S.repeat(Math.max(Z1(T,S)+1,3)),w=c.enter("codeFenced");let q=z.move(x);if(o.lang){const M=c.enter(`codeFencedLang${y}`);q+=z.move(c.safe(o.lang,{before:q,after:" ",encode:["`"],...z.current()})),M()}if(o.lang&&o.meta){const M=c.enter(`codeFencedMeta${y}`);q+=z.move(" "),q+=z.move(c.safe(o.meta,{before:q,after:`
`,encode:["`"],...z.current()})),M()}return q+=z.move(`
`),T&&(q+=z.move(T+`
`)),q+=z.move(x),w(),q}function Im(o,m,c){return(c?"":"    ")+o}function S0(o){const m=o.options.quote||'"';if(m!=='"'&&m!=="'")throw new Error("Cannot serialize title with `"+m+"` for `options.quote`, expected `\"`, or `'`");return m}function Pm(o,m,c,g){const S=S0(c),T=S==='"'?"Quote":"Apostrophe",y=c.enter("definition");let z=c.enter("label");const x=c.createTracker(g);let w=x.move("[");return w+=x.move(c.safe(c.associationId(o),{before:w,after:"]",...x.current()})),w+=x.move("]: "),z(),!o.url||/[\0- \u007F]/.test(o.url)?(z=c.enter("destinationLiteral"),w+=x.move("<"),w+=x.move(c.safe(o.url,{before:w,after:">",...x.current()})),w+=x.move(">")):(z=c.enter("destinationRaw"),w+=x.move(c.safe(o.url,{before:w,after:o.title?" ":`
`,...x.current()}))),z(),o.title&&(z=c.enter(`title${T}`),w+=x.move(" "+S),w+=x.move(c.safe(o.title,{before:w,after:S,...x.current()})),w+=x.move(S),z()),y(),w}function tp(o){const m=o.options.emphasis||"*";if(m!=="*"&&m!=="_")throw new Error("Cannot serialize emphasis with `"+m+"` for `options.emphasis`, expected `*`, or `_`");return m}function ss(o){return"&#x"+o.toString(16).toUpperCase()+";"}function Zd(o,m,c){const g=Qd(o),S=Qd(m);return g===void 0?S===void 0?c==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:S===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:g===1?S===void 0?{inside:!1,outside:!1}:S===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:S===void 0?{inside:!1,outside:!1}:S===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}j0.peek=ep;function j0(o,m,c,g){const S=tp(c),T=c.enter("emphasis"),y=c.createTracker(g),z=y.move(S);let x=y.move(c.containerPhrasing(o,{after:S,before:z,...y.current()}));const w=x.charCodeAt(0),q=Zd(g.before.charCodeAt(g.before.length-1),w,S);q.inside&&(x=ss(w)+x.slice(1));const M=x.charCodeAt(x.length-1),N=Zd(g.after.charCodeAt(0),M,S);N.inside&&(x=x.slice(0,-1)+ss(M));const F=y.move(S);return T(),c.attentionEncodeSurroundingInfo={after:N.outside,before:q.outside},z+x+F}function ep(o,m,c){return c.options.emphasis||"*"}function np(o,m){let c=!1;return V1(o,function(g){if("value"in g&&/\r?\n|\r/.test(g.value)||g.type==="break")return c=!0,K1}),!!((!o.depth||o.depth<3)&&Y0(o)&&(m.options.setext||c))}function lp(o,m,c,g){const S=Math.max(Math.min(6,o.depth||1),1),T=c.createTracker(g);if(np(o,c)){const q=c.enter("headingSetext"),M=c.enter("phrasing"),N=c.containerPhrasing(o,{...T.current(),before:`
`,after:`
`});return M(),q(),N+`
`+(S===1?"=":"-").repeat(N.length-(Math.max(N.lastIndexOf("\r"),N.lastIndexOf(`
`))+1))}const y="#".repeat(S),z=c.enter("headingAtx"),x=c.enter("phrasing");T.move(y+" ");let w=c.containerPhrasing(o,{before:"# ",after:`
`,...T.current()});return/^[\t ]/.test(w)&&(w=ss(w.charCodeAt(0))+w.slice(1)),w=w?y+" "+w:y,c.options.closeAtx&&(w+=" "+y),x(),z(),w}J0.peek=ip;function J0(o){return o.value||""}function ip(){return"<"}W0.peek=up;function W0(o,m,c,g){const S=S0(c),T=S==='"'?"Quote":"Apostrophe",y=c.enter("image");let z=c.enter("label");const x=c.createTracker(g);let w=x.move("![");return w+=x.move(c.safe(o.alt,{before:w,after:"]",...x.current()})),w+=x.move("]("),z(),!o.url&&o.title||/[\0- \u007F]/.test(o.url)?(z=c.enter("destinationLiteral"),w+=x.move("<"),w+=x.move(c.safe(o.url,{before:w,after:">",...x.current()})),w+=x.move(">")):(z=c.enter("destinationRaw"),w+=x.move(c.safe(o.url,{before:w,after:o.title?" ":")",...x.current()}))),z(),o.title&&(z=c.enter(`title${T}`),w+=x.move(" "+S),w+=x.move(c.safe(o.title,{before:w,after:S,...x.current()})),w+=x.move(S),z()),w+=x.move(")"),y(),w}function up(){return"!"}F0.peek=ap;function F0(o,m,c,g){const S=o.referenceType,T=c.enter("imageReference");let y=c.enter("label");const z=c.createTracker(g);let x=z.move("![");const w=c.safe(o.alt,{before:x,after:"]",...z.current()});x+=z.move(w+"]["),y();const q=c.stack;c.stack=[],y=c.enter("reference");const M=c.safe(c.associationId(o),{before:x,after:"]",...z.current()});return y(),c.stack=q,T(),S==="full"||!w||w!==M?x+=z.move(M+"]"):S==="shortcut"?x=x.slice(0,-1):x+=z.move("]"),x}function ap(){return"!"}$0.peek=rp;function $0(o,m,c){let g=o.value||"",S="`",T=-1;for(;new RegExp("(^|[^`])"+S+"([^`]|$)").test(g);)S+="`";for(/[^ \r\n]/.test(g)&&(/^[ \r\n]/.test(g)&&/[ \r\n]$/.test(g)||/^`|`$/.test(g))&&(g=" "+g+" ");++T<c.unsafe.length;){const y=c.unsafe[T],z=c.compilePattern(y);let x;if(y.atBreak)for(;x=z.exec(g);){let w=x.index;g.charCodeAt(w)===10&&g.charCodeAt(w-1)===13&&w--,g=g.slice(0,w)+" "+g.slice(x.index+1)}}return S+g+S}function rp(){return"`"}function I0(o,m){const c=Y0(o);return!!(!m.options.resourceLink&&o.url&&!o.title&&o.children&&o.children.length===1&&o.children[0].type==="text"&&(c===o.url||"mailto:"+c===o.url)&&/^[a-z][a-z+.-]+:/i.test(o.url)&&!/[\0- <>\u007F]/.test(o.url))}P0.peek=fp;function P0(o,m,c,g){const S=S0(c),T=S==='"'?"Quote":"Apostrophe",y=c.createTracker(g);let z,x;if(I0(o,c)){const q=c.stack;c.stack=[],z=c.enter("autolink");let M=y.move("<");return M+=y.move(c.containerPhrasing(o,{before:M,after:">",...y.current()})),M+=y.move(">"),z(),c.stack=q,M}z=c.enter("link"),x=c.enter("label");let w=y.move("[");return w+=y.move(c.containerPhrasing(o,{before:w,after:"](",...y.current()})),w+=y.move("]("),x(),!o.url&&o.title||/[\0- \u007F]/.test(o.url)?(x=c.enter("destinationLiteral"),w+=y.move("<"),w+=y.move(c.safe(o.url,{before:w,after:">",...y.current()})),w+=y.move(">")):(x=c.enter("destinationRaw"),w+=y.move(c.safe(o.url,{before:w,after:o.title?" ":")",...y.current()}))),x(),o.title&&(x=c.enter(`title${T}`),w+=y.move(" "+S),w+=y.move(c.safe(o.title,{before:w,after:S,...y.current()})),w+=y.move(S),x()),w+=y.move(")"),z(),w}function fp(o,m,c){return I0(o,c)?"<":"["}t1.peek=cp;function t1(o,m,c,g){const S=o.referenceType,T=c.enter("linkReference");let y=c.enter("label");const z=c.createTracker(g);let x=z.move("[");const w=c.containerPhrasing(o,{before:x,after:"]",...z.current()});x+=z.move(w+"]["),y();const q=c.stack;c.stack=[],y=c.enter("reference");const M=c.safe(c.associationId(o),{before:x,after:"]",...z.current()});return y(),c.stack=q,T(),S==="full"||!w||w!==M?x+=z.move(M+"]"):S==="shortcut"?x=x.slice(0,-1):x+=z.move("]"),x}function cp(){return"["}function _0(o){const m=o.options.bullet||"*";if(m!=="*"&&m!=="+"&&m!=="-")throw new Error("Cannot serialize items with `"+m+"` for `options.bullet`, expected `*`, `+`, or `-`");return m}function op(o){const m=_0(o),c=o.options.bulletOther;if(!c)return m==="*"?"-":"*";if(c!=="*"&&c!=="+"&&c!=="-")throw new Error("Cannot serialize items with `"+c+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(c===m)throw new Error("Expected `bullet` (`"+m+"`) and `bulletOther` (`"+c+"`) to be different");return c}function sp(o){const m=o.options.bulletOrdered||".";if(m!=="."&&m!==")")throw new Error("Cannot serialize items with `"+m+"` for `options.bulletOrdered`, expected `.` or `)`");return m}function e1(o){const m=o.options.rule||"*";if(m!=="*"&&m!=="-"&&m!=="_")throw new Error("Cannot serialize rules with `"+m+"` for `options.rule`, expected `*`, `-`, or `_`");return m}function hp(o,m,c,g){const S=c.enter("list"),T=c.bulletCurrent;let y=o.ordered?sp(c):_0(c);const z=o.ordered?y==="."?")":".":op(c);let x=m&&c.bulletLastUsed?y===c.bulletLastUsed:!1;if(!o.ordered){const q=o.children?o.children[0]:void 0;if((y==="*"||y==="-")&&q&&(!q.children||!q.children[0])&&c.stack[c.stack.length-1]==="list"&&c.stack[c.stack.length-2]==="listItem"&&c.stack[c.stack.length-3]==="list"&&c.stack[c.stack.length-4]==="listItem"&&c.indexStack[c.indexStack.length-1]===0&&c.indexStack[c.indexStack.length-2]===0&&c.indexStack[c.indexStack.length-3]===0&&(x=!0),e1(c)===y&&q){let M=-1;for(;++M<o.children.length;){const N=o.children[M];if(N&&N.type==="listItem"&&N.children&&N.children[0]&&N.children[0].type==="thematicBreak"){x=!0;break}}}}x&&(y=z),c.bulletCurrent=y;const w=c.containerFlow(o,g);return c.bulletLastUsed=y,c.bulletCurrent=T,S(),w}function dp(o){const m=o.options.listItemIndent||"one";if(m!=="tab"&&m!=="one"&&m!=="mixed")throw new Error("Cannot serialize items with `"+m+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return m}function gp(o,m,c,g){const S=dp(c);let T=c.bulletCurrent||_0(c);m&&m.type==="list"&&m.ordered&&(T=(typeof m.start=="number"&&m.start>-1?m.start:1)+(c.options.incrementListMarker===!1?0:m.children.indexOf(o))+T);let y=T.length+1;(S==="tab"||S==="mixed"&&(m&&m.type==="list"&&m.spread||o.spread))&&(y=Math.ceil(y/4)*4);const z=c.createTracker(g);z.move(T+" ".repeat(y-T.length)),z.shift(y);const x=c.enter("listItem"),w=c.indentLines(c.containerFlow(o,z.current()),q);return x(),w;function q(M,N,F){return N?(F?"":" ".repeat(y))+M:(F?T:T+" ".repeat(y-T.length))+M}}function mp(o,m,c,g){const S=c.enter("paragraph"),T=c.enter("phrasing"),y=c.containerPhrasing(o,g);return T(),S(),y}const pp=G0(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function vp(o,m,c,g){return(o.children.some(function(y){return pp(y)})?c.containerPhrasing:c.containerFlow).call(c,o,g)}function bp(o){const m=o.options.strong||"*";if(m!=="*"&&m!=="_")throw new Error("Cannot serialize strong with `"+m+"` for `options.strong`, expected `*`, or `_`");return m}n1.peek=yp;function n1(o,m,c,g){const S=bp(c),T=c.enter("strong"),y=c.createTracker(g),z=y.move(S+S);let x=y.move(c.containerPhrasing(o,{after:S,before:z,...y.current()}));const w=x.charCodeAt(0),q=Zd(g.before.charCodeAt(g.before.length-1),w,S);q.inside&&(x=ss(w)+x.slice(1));const M=x.charCodeAt(x.length-1),N=Zd(g.after.charCodeAt(0),M,S);N.inside&&(x=x.slice(0,-1)+ss(M));const F=y.move(S+S);return T(),c.attentionEncodeSurroundingInfo={after:N.outside,before:q.outside},z+x+F}function yp(o,m,c){return c.options.strong||"*"}function Sp(o,m,c,g){return c.safe(o.value,g)}function _p(o){const m=o.options.ruleRepetition||3;if(m<3)throw new Error("Cannot serialize rules with repetition `"+m+"` for `options.ruleRepetition`, expected `3` or more");return m}function xp(o,m,c){const g=(e1(c)+(c.options.ruleSpaces?" ":"")).repeat(_p(c));return c.options.ruleSpaces?g.slice(0,-1):g}const l1={blockquote:Km,break:C0,code:$m,definition:Pm,emphasis:j0,hardBreak:C0,heading:lp,html:J0,image:W0,imageReference:F0,inlineCode:$0,link:P0,linkReference:t1,list:hp,listItem:gp,paragraph:mp,root:vp,strong:n1,text:Sp,thematicBreak:xp};function Ap(){return{enter:{table:Tp,tableData:O0,tableHeader:O0,tableRow:kp},exit:{codeText:zp,table:wp,tableData:d0,tableHeader:d0,tableRow:d0}}}function Tp(o){const m=o._align;this.enter({type:"table",align:m.map(function(c){return c==="none"?null:c}),children:[]},o),this.data.inTable=!0}function wp(o){this.exit(o),this.data.inTable=void 0}function kp(o){this.enter({type:"tableRow",children:[]},o)}function d0(o){this.exit(o)}function O0(o){this.enter({type:"tableCell",children:[]},o)}function zp(o){let m=this.resume();this.data.inTable&&(m=m.replace(/\\([\\|])/g,Ep));const c=this.stack[this.stack.length-1];rc(c.type==="inlineCode"),c.value=m,this.exit(o)}function Ep(o,m){return m==="|"?m:o}function Mp(o){const m=o||{},c=m.tableCellPadding,g=m.tablePipeAlign,S=m.stringLength,T=c?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:N,table:y,tableCell:x,tableRow:z}};function y(F,Z,I,et){return w(q(F,I,et),F.align)}function z(F,Z,I,et){const ut=M(F,I,et),dt=w([ut]);return dt.slice(0,dt.indexOf(`
`))}function x(F,Z,I,et){const ut=I.enter("tableCell"),dt=I.enter("phrasing"),it=I.containerPhrasing(F,{...et,before:T,after:T});return dt(),ut(),it}function w(F,Z){return Zm(F,{align:Z,alignDelimiters:g,padding:c,stringLength:S})}function q(F,Z,I){const et=F.children;let ut=-1;const dt=[],it=Z.enter("table");for(;++ut<et.length;)dt[ut]=M(et[ut],Z,I);return it(),dt}function M(F,Z,I){const et=F.children;let ut=-1;const dt=[],it=Z.enter("tableRow");for(;++ut<et.length;)dt[ut]=x(et[ut],F,Z,I);return it(),dt}function N(F,Z,I){let et=l1.inlineCode(F,Z,I);return I.stack.includes("tableCell")&&(et=et.replace(/\|/g,"\\$&")),et}}function Dp(){return{exit:{taskListCheckValueChecked:R0,taskListCheckValueUnchecked:R0,paragraph:Op}}}function Cp(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Rp}}}function R0(o){const m=this.stack[this.stack.length-2];rc(m.type==="listItem"),m.checked=o.type==="taskListCheckValueChecked"}function Op(o){const m=this.stack[this.stack.length-2];if(m&&m.type==="listItem"&&typeof m.checked=="boolean"){const c=this.stack[this.stack.length-1];rc(c.type==="paragraph");const g=c.children[0];if(g&&g.type==="text"){const S=m.children;let T=-1,y;for(;++T<S.length;){const z=S[T];if(z.type==="paragraph"){y=z;break}}y===c&&(g.value=g.value.slice(1),g.value.length===0?c.children.shift():c.position&&g.position&&typeof g.position.start.offset=="number"&&(g.position.start.column++,g.position.start.offset++,c.position.start=Object.assign({},g.position.start)))}}this.exit(o)}function Rp(o,m,c,g){const S=o.children[0],T=typeof o.checked=="boolean"&&S&&S.type==="paragraph",y="["+(o.checked?"x":" ")+"] ",z=c.createTracker(g);T&&z.move(y);let x=l1.listItem(o,m,c,{...g,...z.current()});return T&&(x=x.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,w)),x;function w(q){return q+y}}function Up(){return[dm(),Um(),Nm(),Ap(),Dp()]}function Bp(o){return{extensions:[gm(),Bm(o),qm(),Mp(o),Cp()]}}const Lp={tokenize:Xp,partial:!0},i1={tokenize:Qp,partial:!0},u1={tokenize:Zp,partial:!0},a1={tokenize:Vp,partial:!0},Hp={tokenize:Kp,partial:!0},r1={name:"wwwAutolink",tokenize:Gp,previous:c1},f1={name:"protocolAutolink",tokenize:Yp,previous:o1},Wi={name:"emailAutolink",tokenize:qp,previous:s1},yi={};function Np(){return{text:yi}}let vr=48;for(;vr<123;)yi[vr]=Wi,vr++,vr===58?vr=65:vr===91&&(vr=97);yi[43]=Wi;yi[45]=Wi;yi[46]=Wi;yi[95]=Wi;yi[72]=[Wi,f1];yi[104]=[Wi,f1];yi[87]=[Wi,r1];yi[119]=[Wi,r1];function qp(o,m,c){const g=this;let S,T;return y;function y(M){return!m0(M)||!s1.call(g,g.previous)||x0(g.events)?c(M):(o.enter("literalAutolink"),o.enter("literalAutolinkEmail"),z(M))}function z(M){return m0(M)?(o.consume(M),z):M===64?(o.consume(M),x):c(M)}function x(M){return M===46?o.check(Hp,q,w)(M):M===45||M===95||y0(M)?(T=!0,o.consume(M),x):q(M)}function w(M){return o.consume(M),S=!0,x}function q(M){return T&&S&&os(g.previous)?(o.exit("literalAutolinkEmail"),o.exit("literalAutolink"),m(M)):c(M)}}function Gp(o,m,c){const g=this;return S;function S(y){return y!==87&&y!==119||!c1.call(g,g.previous)||x0(g.events)?c(y):(o.enter("literalAutolink"),o.enter("literalAutolinkWww"),o.check(Lp,o.attempt(i1,o.attempt(u1,T),c),c)(y))}function T(y){return o.exit("literalAutolinkWww"),o.exit("literalAutolink"),m(y)}}function Yp(o,m,c){const g=this;let S="",T=!1;return y;function y(M){return(M===72||M===104)&&o1.call(g,g.previous)&&!x0(g.events)?(o.enter("literalAutolink"),o.enter("literalAutolinkHttp"),S+=String.fromCodePoint(M),o.consume(M),z):c(M)}function z(M){if(os(M)&&S.length<5)return S+=String.fromCodePoint(M),o.consume(M),z;if(M===58){const N=S.toLowerCase();if(N==="http"||N==="https")return o.consume(M),x}return c(M)}function x(M){return M===47?(o.consume(M),T?w:(T=!0,x)):c(M)}function w(M){return M===null||j1(M)||il(M)||ac(M)||b0(M)?c(M):o.attempt(i1,o.attempt(u1,q),c)(M)}function q(M){return o.exit("literalAutolinkHttp"),o.exit("literalAutolink"),m(M)}}function Xp(o,m,c){let g=0;return S;function S(y){return(y===87||y===119)&&g<3?(g++,o.consume(y),S):y===46&&g===3?(o.consume(y),T):c(y)}function T(y){return y===null?c(y):m(y)}}function Qp(o,m,c){let g,S,T;return y;function y(w){return w===46||w===95?o.check(a1,x,z)(w):w===null||il(w)||ac(w)||w!==45&&b0(w)?x(w):(T=!0,o.consume(w),y)}function z(w){return w===95?g=!0:(S=g,g=void 0),o.consume(w),y}function x(w){return S||g||!T?c(w):m(w)}}function Zp(o,m){let c=0,g=0;return S;function S(y){return y===40?(c++,o.consume(y),S):y===41&&g<c?T(y):y===33||y===34||y===38||y===39||y===41||y===42||y===44||y===46||y===58||y===59||y===60||y===63||y===93||y===95||y===126?o.check(a1,m,T)(y):y===null||il(y)||ac(y)?m(y):(o.consume(y),S)}function T(y){return y===41&&g++,o.consume(y),S}}function Vp(o,m,c){return g;function g(z){return z===33||z===34||z===39||z===41||z===42||z===44||z===46||z===58||z===59||z===63||z===95||z===126?(o.consume(z),g):z===38?(o.consume(z),T):z===93?(o.consume(z),S):z===60||z===null||il(z)||ac(z)?m(z):c(z)}function S(z){return z===null||z===40||z===91||il(z)||ac(z)?m(z):g(z)}function T(z){return os(z)?y(z):c(z)}function y(z){return z===59?(o.consume(z),g):os(z)?(o.consume(z),y):c(z)}}function Kp(o,m,c){return g;function g(T){return o.consume(T),S}function S(T){return y0(T)?c(T):m(T)}}function c1(o){return o===null||o===40||o===42||o===95||o===91||o===93||o===126||il(o)}function o1(o){return!os(o)}function s1(o){return!(o===47||m0(o))}function m0(o){return o===43||o===45||o===46||o===95||y0(o)}function x0(o){let m=o.length,c=!1;for(;m--;){const g=o[m][1];if((g.type==="labelLink"||g.type==="labelImage")&&!g._balanced){c=!0;break}if(g._gfmAutolinkLiteralWalkedInto){c=!1;break}}return o.length>0&&!c&&(o[o.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),c}const jp={tokenize:ev,partial:!0};function Jp(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:Ip,continuation:{tokenize:Pp},exit:tv}},text:{91:{name:"gfmFootnoteCall",tokenize:$p},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:Wp,resolveTo:Fp}}}}function Wp(o,m,c){const g=this;let S=g.events.length;const T=g.parser.gfmFootnotes||(g.parser.gfmFootnotes=[]);let y;for(;S--;){const x=g.events[S][1];if(x.type==="labelImage"){y=x;break}if(x.type==="gfmFootnoteCall"||x.type==="labelLink"||x.type==="label"||x.type==="image"||x.type==="link")break}return z;function z(x){if(!y||!y._balanced)return c(x);const w=hs(g.sliceSerialize({start:y.end,end:g.now()}));return w.codePointAt(0)!==94||!T.includes(w.slice(1))?c(x):(o.enter("gfmFootnoteCallLabelMarker"),o.consume(x),o.exit("gfmFootnoteCallLabelMarker"),m(x))}}function Fp(o,m){let c=o.length;for(;c--;)if(o[c][1].type==="labelImage"&&o[c][0]==="enter"){o[c][1];break}o[c+1][1].type="data",o[c+3][1].type="gfmFootnoteCallLabelMarker";const g={type:"gfmFootnoteCall",start:Object.assign({},o[c+3][1].start),end:Object.assign({},o[o.length-1][1].end)},S={type:"gfmFootnoteCallMarker",start:Object.assign({},o[c+3][1].end),end:Object.assign({},o[c+3][1].end)};S.end.column++,S.end.offset++,S.end._bufferIndex++;const T={type:"gfmFootnoteCallString",start:Object.assign({},S.end),end:Object.assign({},o[o.length-1][1].start)},y={type:"chunkString",contentType:"string",start:Object.assign({},T.start),end:Object.assign({},T.end)},z=[o[c+1],o[c+2],["enter",g,m],o[c+3],o[c+4],["enter",S,m],["exit",S,m],["enter",T,m],["enter",y,m],["exit",y,m],["exit",T,m],o[o.length-2],o[o.length-1],["exit",g,m]];return o.splice(c,o.length-c+1,...z),o}function $p(o,m,c){const g=this,S=g.parser.gfmFootnotes||(g.parser.gfmFootnotes=[]);let T=0,y;return z;function z(M){return o.enter("gfmFootnoteCall"),o.enter("gfmFootnoteCallLabelMarker"),o.consume(M),o.exit("gfmFootnoteCallLabelMarker"),x}function x(M){return M!==94?c(M):(o.enter("gfmFootnoteCallMarker"),o.consume(M),o.exit("gfmFootnoteCallMarker"),o.enter("gfmFootnoteCallString"),o.enter("chunkString").contentType="string",w)}function w(M){if(T>999||M===93&&!y||M===null||M===91||il(M))return c(M);if(M===93){o.exit("chunkString");const N=o.exit("gfmFootnoteCallString");return S.includes(hs(g.sliceSerialize(N)))?(o.enter("gfmFootnoteCallLabelMarker"),o.consume(M),o.exit("gfmFootnoteCallLabelMarker"),o.exit("gfmFootnoteCall"),m):c(M)}return il(M)||(y=!0),T++,o.consume(M),M===92?q:w}function q(M){return M===91||M===92||M===93?(o.consume(M),T++,w):w(M)}}function Ip(o,m,c){const g=this,S=g.parser.gfmFootnotes||(g.parser.gfmFootnotes=[]);let T,y=0,z;return x;function x(Z){return o.enter("gfmFootnoteDefinition")._container=!0,o.enter("gfmFootnoteDefinitionLabel"),o.enter("gfmFootnoteDefinitionLabelMarker"),o.consume(Z),o.exit("gfmFootnoteDefinitionLabelMarker"),w}function w(Z){return Z===94?(o.enter("gfmFootnoteDefinitionMarker"),o.consume(Z),o.exit("gfmFootnoteDefinitionMarker"),o.enter("gfmFootnoteDefinitionLabelString"),o.enter("chunkString").contentType="string",q):c(Z)}function q(Z){if(y>999||Z===93&&!z||Z===null||Z===91||il(Z))return c(Z);if(Z===93){o.exit("chunkString");const I=o.exit("gfmFootnoteDefinitionLabelString");return T=hs(g.sliceSerialize(I)),o.enter("gfmFootnoteDefinitionLabelMarker"),o.consume(Z),o.exit("gfmFootnoteDefinitionLabelMarker"),o.exit("gfmFootnoteDefinitionLabel"),N}return il(Z)||(z=!0),y++,o.consume(Z),Z===92?M:q}function M(Z){return Z===91||Z===92||Z===93?(o.consume(Z),y++,q):q(Z)}function N(Z){return Z===58?(o.enter("definitionMarker"),o.consume(Z),o.exit("definitionMarker"),S.includes(T)||S.push(T),Pu(o,F,"gfmFootnoteDefinitionWhitespace")):c(Z)}function F(Z){return m(Z)}}function Pp(o,m,c){return o.check(J1,m,o.attempt(jp,m,c))}function tv(o){o.exit("gfmFootnoteDefinition")}function ev(o,m,c){const g=this;return Pu(o,S,"gfmFootnoteDefinitionIndent",5);function S(T){const y=g.events[g.events.length-1];return y&&y[1].type==="gfmFootnoteDefinitionIndent"&&y[2].sliceSerialize(y[1],!0).length===4?m(T):c(T)}}function nv(o){let c=(o||{}).singleTilde;const g={name:"strikethrough",tokenize:T,resolveAll:S};return c==null&&(c=!0),{text:{126:g},insideSpan:{null:[g]},attentionMarkers:{null:[126]}};function S(y,z){let x=-1;for(;++x<y.length;)if(y[x][0]==="enter"&&y[x][1].type==="strikethroughSequenceTemporary"&&y[x][1]._close){let w=x;for(;w--;)if(y[w][0]==="exit"&&y[w][1].type==="strikethroughSequenceTemporary"&&y[w][1]._open&&y[x][1].end.offset-y[x][1].start.offset===y[w][1].end.offset-y[w][1].start.offset){y[x][1].type="strikethroughSequence",y[w][1].type="strikethroughSequence";const q={type:"strikethrough",start:Object.assign({},y[w][1].start),end:Object.assign({},y[x][1].end)},M={type:"strikethroughText",start:Object.assign({},y[w][1].end),end:Object.assign({},y[x][1].start)},N=[["enter",q,z],["enter",y[w][1],z],["exit",y[w][1],z],["enter",M,z]],F=z.parser.constructs.insideSpan.null;F&&a0(N,N.length,0,W1(F,y.slice(w+1,x),z)),a0(N,N.length,0,[["exit",M,z],["enter",y[x][1],z],["exit",y[x][1],z],["exit",q,z]]),a0(y,w-1,x-w+3,N),x=w+N.length-2;break}}for(x=-1;++x<y.length;)y[x][1].type==="strikethroughSequenceTemporary"&&(y[x][1].type="data");return y}function T(y,z,x){const w=this.previous,q=this.events;let M=0;return N;function N(Z){return w===126&&q[q.length-1][1].type!=="characterEscape"?x(Z):(y.enter("strikethroughSequenceTemporary"),F(Z))}function F(Z){const I=Qd(w);if(Z===126)return M>1?x(Z):(y.consume(Z),M++,F);if(M<2&&!c)return x(Z);const et=y.exit("strikethroughSequenceTemporary"),ut=Qd(Z);return et._open=!ut||ut===2&&!!I,et._close=!I||I===2&&!!ut,z(Z)}}}class lv{constructor(){this.map=[]}add(m,c,g){iv(this,m,c,g)}consume(m){if(this.map.sort(function(T,y){return T[0]-y[0]}),this.map.length===0)return;let c=this.map.length;const g=[];for(;c>0;)c-=1,g.push(m.slice(this.map[c][0]+this.map[c][1]),this.map[c][2]),m.length=this.map[c][0];g.push(m.slice()),m.length=0;let S=g.pop();for(;S;){for(const T of S)m.push(T);S=g.pop()}this.map.length=0}}function iv(o,m,c,g){let S=0;if(!(c===0&&g.length===0)){for(;S<o.map.length;){if(o.map[S][0]===m){o.map[S][1]+=c,o.map[S][2].push(...g);return}S+=1}o.map.push([m,c,g])}}function uv(o,m){let c=!1;const g=[];for(;m<o.length;){const S=o[m];if(c){if(S[0]==="enter")S[1].type==="tableContent"&&g.push(o[m+1][1].type==="tableDelimiterMarker"?"left":"none");else if(S[1].type==="tableContent"){if(o[m-1][1].type==="tableDelimiterMarker"){const T=g.length-1;g[T]=g[T]==="left"?"center":"right"}}else if(S[1].type==="tableDelimiterRow")break}else S[0]==="enter"&&S[1].type==="tableDelimiterRow"&&(c=!0);m+=1}return g}function av(){return{flow:{null:{name:"table",tokenize:rv,resolveAll:fv}}}}function rv(o,m,c){const g=this;let S=0,T=0,y;return z;function z(K){let Re=g.events.length-1;for(;Re>-1;){const en=g.events[Re][1].type;if(en==="lineEnding"||en==="linePrefix")Re--;else break}const Et=Re>-1?g.events[Re][1].type:null,Qt=Et==="tableHead"||Et==="tableRow"?Lt:x;return Qt===Lt&&g.parser.lazy[g.now().line]?c(K):Qt(K)}function x(K){return o.enter("tableHead"),o.enter("tableRow"),w(K)}function w(K){return K===124||(y=!0,T+=1),q(K)}function q(K){return K===null?c(K):fs(K)?T>1?(T=0,g.interrupt=!0,o.exit("tableRow"),o.enter("lineEnding"),o.consume(K),o.exit("lineEnding"),F):c(K):ec(K)?Pu(o,q,"whitespace")(K):(T+=1,y&&(y=!1,S+=1),K===124?(o.enter("tableCellDivider"),o.consume(K),o.exit("tableCellDivider"),y=!0,q):(o.enter("data"),M(K)))}function M(K){return K===null||K===124||il(K)?(o.exit("data"),q(K)):(o.consume(K),K===92?N:M)}function N(K){return K===92||K===124?(o.consume(K),M):M(K)}function F(K){return g.interrupt=!1,g.parser.lazy[g.now().line]?c(K):(o.enter("tableDelimiterRow"),y=!1,ec(K)?Pu(o,Z,"linePrefix",g.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(K):Z(K))}function Z(K){return K===45||K===58?et(K):K===124?(y=!0,o.enter("tableCellDivider"),o.consume(K),o.exit("tableCellDivider"),I):Ct(K)}function I(K){return ec(K)?Pu(o,et,"whitespace")(K):et(K)}function et(K){return K===58?(T+=1,y=!0,o.enter("tableDelimiterMarker"),o.consume(K),o.exit("tableDelimiterMarker"),ut):K===45?(T+=1,ut(K)):K===null||fs(K)?Ut(K):Ct(K)}function ut(K){return K===45?(o.enter("tableDelimiterFiller"),dt(K)):Ct(K)}function dt(K){return K===45?(o.consume(K),dt):K===58?(y=!0,o.exit("tableDelimiterFiller"),o.enter("tableDelimiterMarker"),o.consume(K),o.exit("tableDelimiterMarker"),it):(o.exit("tableDelimiterFiller"),it(K))}function it(K){return ec(K)?Pu(o,Ut,"whitespace")(K):Ut(K)}function Ut(K){return K===124?Z(K):K===null||fs(K)?!y||S!==T?Ct(K):(o.exit("tableDelimiterRow"),o.exit("tableHead"),m(K)):Ct(K)}function Ct(K){return c(K)}function Lt(K){return o.enter("tableRow"),$t(K)}function $t(K){return K===124?(o.enter("tableCellDivider"),o.consume(K),o.exit("tableCellDivider"),$t):K===null||fs(K)?(o.exit("tableRow"),m(K)):ec(K)?Pu(o,$t,"whitespace")(K):(o.enter("data"),St(K))}function St(K){return K===null||K===124||il(K)?(o.exit("data"),$t(K)):(o.consume(K),K===92?ue:St)}function ue(K){return K===92||K===124?(o.consume(K),St):St(K)}}function fv(o,m){let c=-1,g=!0,S=0,T=[0,0,0,0],y=[0,0,0,0],z=!1,x=0,w,q,M;const N=new lv;for(;++c<o.length;){const F=o[c],Z=F[1];F[0]==="enter"?Z.type==="tableHead"?(z=!1,x!==0&&(U0(N,m,x,w,q),q=void 0,x=0),w={type:"table",start:Object.assign({},Z.start),end:Object.assign({},Z.end)},N.add(c,0,[["enter",w,m]])):Z.type==="tableRow"||Z.type==="tableDelimiterRow"?(g=!0,M=void 0,T=[0,0,0,0],y=[0,c+1,0,0],z&&(z=!1,q={type:"tableBody",start:Object.assign({},Z.start),end:Object.assign({},Z.end)},N.add(c,0,[["enter",q,m]])),S=Z.type==="tableDelimiterRow"?2:q?3:1):S&&(Z.type==="data"||Z.type==="tableDelimiterMarker"||Z.type==="tableDelimiterFiller")?(g=!1,y[2]===0&&(T[1]!==0&&(y[0]=y[1],M=Xd(N,m,T,S,void 0,M),T=[0,0,0,0]),y[2]=c)):Z.type==="tableCellDivider"&&(g?g=!1:(T[1]!==0&&(y[0]=y[1],M=Xd(N,m,T,S,void 0,M)),T=y,y=[T[1],c,0,0])):Z.type==="tableHead"?(z=!0,x=c):Z.type==="tableRow"||Z.type==="tableDelimiterRow"?(x=c,T[1]!==0?(y[0]=y[1],M=Xd(N,m,T,S,c,M)):y[1]!==0&&(M=Xd(N,m,y,S,c,M)),S=0):S&&(Z.type==="data"||Z.type==="tableDelimiterMarker"||Z.type==="tableDelimiterFiller")&&(y[3]=c)}for(x!==0&&U0(N,m,x,w,q),N.consume(m.events),c=-1;++c<m.events.length;){const F=m.events[c];F[0]==="enter"&&F[1].type==="table"&&(F[1]._align=uv(m.events,c))}return o}function Xd(o,m,c,g,S,T){const y=g===1?"tableHeader":g===2?"tableDelimiter":"tableData",z="tableContent";c[0]!==0&&(T.end=Object.assign({},nc(m.events,c[0])),o.add(c[0],0,[["exit",T,m]]));const x=nc(m.events,c[1]);if(T={type:y,start:Object.assign({},x),end:Object.assign({},x)},o.add(c[1],0,[["enter",T,m]]),c[2]!==0){const w=nc(m.events,c[2]),q=nc(m.events,c[3]),M={type:z,start:Object.assign({},w),end:Object.assign({},q)};if(o.add(c[2],0,[["enter",M,m]]),g!==2){const N=m.events[c[2]],F=m.events[c[3]];if(N[1].end=Object.assign({},F[1].end),N[1].type="chunkText",N[1].contentType="text",c[3]>c[2]+1){const Z=c[2]+1,I=c[3]-c[2]-1;o.add(Z,I,[])}}o.add(c[3]+1,0,[["exit",M,m]])}return S!==void 0&&(T.end=Object.assign({},nc(m.events,S)),o.add(S,0,[["exit",T,m]]),T=void 0),T}function U0(o,m,c,g,S){const T=[],y=nc(m.events,c);S&&(S.end=Object.assign({},y),T.push(["exit",S,m])),g.end=Object.assign({},y),T.push(["exit",g,m]),o.add(c+1,0,T)}function nc(o,m){const c=o[m],g=c[0]==="enter"?"start":"end";return c[1][g]}const cv={name:"tasklistCheck",tokenize:sv};function ov(){return{text:{91:cv}}}function sv(o,m,c){const g=this;return S;function S(x){return g.previous!==null||!g._gfmTasklistFirstContentOfListItem?c(x):(o.enter("taskListCheck"),o.enter("taskListCheckMarker"),o.consume(x),o.exit("taskListCheckMarker"),T)}function T(x){return il(x)?(o.enter("taskListCheckValueUnchecked"),o.consume(x),o.exit("taskListCheckValueUnchecked"),y):x===88||x===120?(o.enter("taskListCheckValueChecked"),o.consume(x),o.exit("taskListCheckValueChecked"),y):c(x)}function y(x){return x===93?(o.enter("taskListCheckMarker"),o.consume(x),o.exit("taskListCheckMarker"),o.exit("taskListCheck"),z):c(x)}function z(x){return fs(x)?m(x):ec(x)?o.check({tokenize:hv},m,c)(x):c(x)}}function hv(o,m,c){return Pu(o,g,"whitespace");function g(S){return S===null?c(S):m(S)}}function dv(o){return F1([Np(),Jp(),nv(o),av(),ov()])}const gv={};function mv(o){const m=this,c=o||gv,g=m.data(),S=g.micromarkExtensions||(g.micromarkExtensions=[]),T=g.fromMarkdownExtensions||(g.fromMarkdownExtensions=[]),y=g.toMarkdownExtensions||(g.toMarkdownExtensions=[]);S.push(dv(c)),T.push(Up()),y.push(Bp(c))}const pv={'code[class*="language-"]':{background:"hsl(220, 13%, 18%)",color:"hsl(220, 14%, 71%)",textShadow:"0 1px rgba(0, 0, 0, 0.3)",fontFamily:'"Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace',direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",lineHeight:"1.5",MozTabSize:"2",OTabSize:"2",tabSize:"2",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none"},'pre[class*="language-"]':{background:"hsl(220, 13%, 18%)",color:"hsl(220, 14%, 71%)",textShadow:"0 1px rgba(0, 0, 0, 0.3)",fontFamily:'"Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace',direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",lineHeight:"1.5",MozTabSize:"2",OTabSize:"2",tabSize:"2",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",padding:"1em",margin:"0.5em 0",overflow:"auto",borderRadius:"0.3em"},'code[class*="language-"]::-moz-selection':{background:"hsl(220, 13%, 28%)",color:"inherit",textShadow:"none"},'code[class*="language-"] *::-moz-selection':{background:"hsl(220, 13%, 28%)",color:"inherit",textShadow:"none"},'pre[class*="language-"] *::-moz-selection':{background:"hsl(220, 13%, 28%)",color:"inherit",textShadow:"none"},'code[class*="language-"]::selection':{background:"hsl(220, 13%, 28%)",color:"inherit",textShadow:"none"},'code[class*="language-"] *::selection':{background:"hsl(220, 13%, 28%)",color:"inherit",textShadow:"none"},'pre[class*="language-"] *::selection':{background:"hsl(220, 13%, 28%)",color:"inherit",textShadow:"none"},':not(pre) > code[class*="language-"]':{padding:"0.2em 0.3em",borderRadius:"0.3em",whiteSpace:"normal"},comment:{color:"hsl(220, 10%, 40%)",fontStyle:"italic"},prolog:{color:"hsl(220, 10%, 40%)"},cdata:{color:"hsl(220, 10%, 40%)"},doctype:{color:"hsl(220, 14%, 71%)"},punctuation:{color:"hsl(220, 14%, 71%)"},entity:{color:"hsl(220, 14%, 71%)",cursor:"help"},"attr-name":{color:"hsl(29, 54%, 61%)"},"class-name":{color:"hsl(29, 54%, 61%)"},boolean:{color:"hsl(29, 54%, 61%)"},constant:{color:"hsl(29, 54%, 61%)"},number:{color:"hsl(29, 54%, 61%)"},atrule:{color:"hsl(29, 54%, 61%)"},keyword:{color:"hsl(286, 60%, 67%)"},property:{color:"hsl(355, 65%, 65%)"},tag:{color:"hsl(355, 65%, 65%)"},symbol:{color:"hsl(355, 65%, 65%)"},deleted:{color:"hsl(355, 65%, 65%)"},important:{color:"hsl(355, 65%, 65%)"},selector:{color:"hsl(95, 38%, 62%)"},string:{color:"hsl(95, 38%, 62%)"},char:{color:"hsl(95, 38%, 62%)"},builtin:{color:"hsl(95, 38%, 62%)"},inserted:{color:"hsl(95, 38%, 62%)"},regex:{color:"hsl(95, 38%, 62%)"},"attr-value":{color:"hsl(95, 38%, 62%)"},"attr-value > .token.punctuation":{color:"hsl(95, 38%, 62%)"},variable:{color:"hsl(207, 82%, 66%)"},operator:{color:"hsl(207, 82%, 66%)"},function:{color:"hsl(207, 82%, 66%)"},url:{color:"hsl(187, 47%, 55%)"},"attr-value > .token.punctuation.attr-equals":{color:"hsl(220, 14%, 71%)"},"special-attr > .token.attr-value > .token.value.css":{color:"hsl(220, 14%, 71%)"},".language-css .token.selector":{color:"hsl(355, 65%, 65%)"},".language-css .token.property":{color:"hsl(220, 14%, 71%)"},".language-css .token.function":{color:"hsl(187, 47%, 55%)"},".language-css .token.url > .token.function":{color:"hsl(187, 47%, 55%)"},".language-css .token.url > .token.string.url":{color:"hsl(95, 38%, 62%)"},".language-css .token.important":{color:"hsl(286, 60%, 67%)"},".language-css .token.atrule .token.rule":{color:"hsl(286, 60%, 67%)"},".language-javascript .token.operator":{color:"hsl(286, 60%, 67%)"},".language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation":{color:"hsl(5, 48%, 51%)"},".language-json .token.operator":{color:"hsl(220, 14%, 71%)"},".language-json .token.null.keyword":{color:"hsl(29, 54%, 61%)"},".language-markdown .token.url":{color:"hsl(220, 14%, 71%)"},".language-markdown .token.url > .token.operator":{color:"hsl(220, 14%, 71%)"},".language-markdown .token.url-reference.url > .token.string":{color:"hsl(220, 14%, 71%)"},".language-markdown .token.url > .token.content":{color:"hsl(207, 82%, 66%)"},".language-markdown .token.url > .token.url":{color:"hsl(187, 47%, 55%)"},".language-markdown .token.url-reference.url":{color:"hsl(187, 47%, 55%)"},".language-markdown .token.blockquote.punctuation":{color:"hsl(220, 10%, 40%)",fontStyle:"italic"},".language-markdown .token.hr.punctuation":{color:"hsl(220, 10%, 40%)",fontStyle:"italic"},".language-markdown .token.code-snippet":{color:"hsl(95, 38%, 62%)"},".language-markdown .token.bold .token.content":{color:"hsl(29, 54%, 61%)"},".language-markdown .token.italic .token.content":{color:"hsl(286, 60%, 67%)"},".language-markdown .token.strike .token.content":{color:"hsl(355, 65%, 65%)"},".language-markdown .token.strike .token.punctuation":{color:"hsl(355, 65%, 65%)"},".language-markdown .token.list.punctuation":{color:"hsl(355, 65%, 65%)"},".language-markdown .token.title.important > .token.punctuation":{color:"hsl(355, 65%, 65%)"},bold:{fontWeight:"bold"},italic:{fontStyle:"italic"},namespace:{Opacity:"0.8"},"token.tab:not(:empty):before":{color:"hsla(220, 14%, 71%, 0.15)",textShadow:"none"},"token.cr:before":{color:"hsla(220, 14%, 71%, 0.15)",textShadow:"none"},"token.lf:before":{color:"hsla(220, 14%, 71%, 0.15)",textShadow:"none"},"token.space:before":{color:"hsla(220, 14%, 71%, 0.15)",textShadow:"none"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item":{marginRight:"0.4em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button":{background:"hsl(220, 13%, 26%)",color:"hsl(220, 9%, 55%)",padding:"0.1em 0.4em",borderRadius:"0.3em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a":{background:"hsl(220, 13%, 26%)",color:"hsl(220, 9%, 55%)",padding:"0.1em 0.4em",borderRadius:"0.3em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span":{background:"hsl(220, 13%, 26%)",color:"hsl(220, 9%, 55%)",padding:"0.1em 0.4em",borderRadius:"0.3em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover":{background:"hsl(220, 13%, 28%)",color:"hsl(220, 14%, 71%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus":{background:"hsl(220, 13%, 28%)",color:"hsl(220, 14%, 71%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover":{background:"hsl(220, 13%, 28%)",color:"hsl(220, 14%, 71%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus":{background:"hsl(220, 13%, 28%)",color:"hsl(220, 14%, 71%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover":{background:"hsl(220, 13%, 28%)",color:"hsl(220, 14%, 71%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus":{background:"hsl(220, 13%, 28%)",color:"hsl(220, 14%, 71%)"},".line-highlight.line-highlight":{background:"hsla(220, 100%, 80%, 0.04)"},".line-highlight.line-highlight:before":{background:"hsl(220, 13%, 26%)",color:"hsl(220, 14%, 71%)",padding:"0.1em 0.6em",borderRadius:"0.3em",boxShadow:"0 2px 0 0 rgba(0, 0, 0, 0.2)"},".line-highlight.line-highlight[data-end]:after":{background:"hsl(220, 13%, 26%)",color:"hsl(220, 14%, 71%)",padding:"0.1em 0.6em",borderRadius:"0.3em",boxShadow:"0 2px 0 0 rgba(0, 0, 0, 0.2)"},"pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before":{backgroundColor:"hsla(220, 100%, 80%, 0.04)"},".line-numbers.line-numbers .line-numbers-rows":{borderRightColor:"hsla(220, 14%, 71%, 0.15)"},".command-line .command-line-prompt":{borderRightColor:"hsla(220, 14%, 71%, 0.15)"},".line-numbers .line-numbers-rows > span:before":{color:"hsl(220, 14%, 45%)"},".command-line .command-line-prompt > span:before":{color:"hsl(220, 14%, 45%)"},".rainbow-braces .token.token.punctuation.brace-level-1":{color:"hsl(355, 65%, 65%)"},".rainbow-braces .token.token.punctuation.brace-level-5":{color:"hsl(355, 65%, 65%)"},".rainbow-braces .token.token.punctuation.brace-level-9":{color:"hsl(355, 65%, 65%)"},".rainbow-braces .token.token.punctuation.brace-level-2":{color:"hsl(95, 38%, 62%)"},".rainbow-braces .token.token.punctuation.brace-level-6":{color:"hsl(95, 38%, 62%)"},".rainbow-braces .token.token.punctuation.brace-level-10":{color:"hsl(95, 38%, 62%)"},".rainbow-braces .token.token.punctuation.brace-level-3":{color:"hsl(207, 82%, 66%)"},".rainbow-braces .token.token.punctuation.brace-level-7":{color:"hsl(207, 82%, 66%)"},".rainbow-braces .token.token.punctuation.brace-level-11":{color:"hsl(207, 82%, 66%)"},".rainbow-braces .token.token.punctuation.brace-level-4":{color:"hsl(286, 60%, 67%)"},".rainbow-braces .token.token.punctuation.brace-level-8":{color:"hsl(286, 60%, 67%)"},".rainbow-braces .token.token.punctuation.brace-level-12":{color:"hsl(286, 60%, 67%)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix)":{backgroundColor:"hsla(353, 100%, 66%, 0.15)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix)":{backgroundColor:"hsla(353, 100%, 66%, 0.15)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix)::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix) *::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix)::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix) *::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix)":{backgroundColor:"hsla(137, 100%, 55%, 0.15)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix)":{backgroundColor:"hsla(137, 100%, 55%, 0.15)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix)::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix) *::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix)::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix) *::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},".prism-previewer.prism-previewer:before":{borderColor:"hsl(224, 13%, 17%)"},".prism-previewer-gradient.prism-previewer-gradient div":{borderColor:"hsl(224, 13%, 17%)",borderRadius:"0.3em"},".prism-previewer-color.prism-previewer-color:before":{borderRadius:"0.3em"},".prism-previewer-easing.prism-previewer-easing:before":{borderRadius:"0.3em"},".prism-previewer.prism-previewer:after":{borderTopColor:"hsl(224, 13%, 17%)"},".prism-previewer-flipped.prism-previewer-flipped.after":{borderBottomColor:"hsl(224, 13%, 17%)"},".prism-previewer-angle.prism-previewer-angle:before":{background:"hsl(219, 13%, 22%)"},".prism-previewer-time.prism-previewer-time:before":{background:"hsl(219, 13%, 22%)"},".prism-previewer-easing.prism-previewer-easing":{background:"hsl(219, 13%, 22%)"},".prism-previewer-angle.prism-previewer-angle circle":{stroke:"hsl(220, 14%, 71%)",strokeOpacity:"1"},".prism-previewer-time.prism-previewer-time circle":{stroke:"hsl(220, 14%, 71%)",strokeOpacity:"1"},".prism-previewer-easing.prism-previewer-easing circle":{stroke:"hsl(220, 14%, 71%)",fill:"transparent"},".prism-previewer-easing.prism-previewer-easing path":{stroke:"hsl(220, 14%, 71%)"},".prism-previewer-easing.prism-previewer-easing line":{stroke:"hsl(220, 14%, 71%)"}},vv={'code[class*="language-"]':{background:"hsl(230, 1%, 98%)",color:"hsl(230, 8%, 24%)",fontFamily:'"Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace',direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",lineHeight:"1.5",MozTabSize:"2",OTabSize:"2",tabSize:"2",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none"},'pre[class*="language-"]':{background:"hsl(230, 1%, 98%)",color:"hsl(230, 8%, 24%)",fontFamily:'"Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace',direction:"ltr",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",lineHeight:"1.5",MozTabSize:"2",OTabSize:"2",tabSize:"2",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",padding:"1em",margin:"0.5em 0",overflow:"auto",borderRadius:"0.3em"},'code[class*="language-"]::-moz-selection':{background:"hsl(230, 1%, 90%)",color:"inherit"},'code[class*="language-"] *::-moz-selection':{background:"hsl(230, 1%, 90%)",color:"inherit"},'pre[class*="language-"] *::-moz-selection':{background:"hsl(230, 1%, 90%)",color:"inherit"},'code[class*="language-"]::selection':{background:"hsl(230, 1%, 90%)",color:"inherit"},'code[class*="language-"] *::selection':{background:"hsl(230, 1%, 90%)",color:"inherit"},'pre[class*="language-"] *::selection':{background:"hsl(230, 1%, 90%)",color:"inherit"},':not(pre) > code[class*="language-"]':{padding:"0.2em 0.3em",borderRadius:"0.3em",whiteSpace:"normal"},comment:{color:"hsl(230, 4%, 64%)",fontStyle:"italic"},prolog:{color:"hsl(230, 4%, 64%)"},cdata:{color:"hsl(230, 4%, 64%)"},doctype:{color:"hsl(230, 8%, 24%)"},punctuation:{color:"hsl(230, 8%, 24%)"},entity:{color:"hsl(230, 8%, 24%)",cursor:"help"},"attr-name":{color:"hsl(35, 99%, 36%)"},"class-name":{color:"hsl(35, 99%, 36%)"},boolean:{color:"hsl(35, 99%, 36%)"},constant:{color:"hsl(35, 99%, 36%)"},number:{color:"hsl(35, 99%, 36%)"},atrule:{color:"hsl(35, 99%, 36%)"},keyword:{color:"hsl(301, 63%, 40%)"},property:{color:"hsl(5, 74%, 59%)"},tag:{color:"hsl(5, 74%, 59%)"},symbol:{color:"hsl(5, 74%, 59%)"},deleted:{color:"hsl(5, 74%, 59%)"},important:{color:"hsl(5, 74%, 59%)"},selector:{color:"hsl(119, 34%, 47%)"},string:{color:"hsl(119, 34%, 47%)"},char:{color:"hsl(119, 34%, 47%)"},builtin:{color:"hsl(119, 34%, 47%)"},inserted:{color:"hsl(119, 34%, 47%)"},regex:{color:"hsl(119, 34%, 47%)"},"attr-value":{color:"hsl(119, 34%, 47%)"},"attr-value > .token.punctuation":{color:"hsl(119, 34%, 47%)"},variable:{color:"hsl(221, 87%, 60%)"},operator:{color:"hsl(221, 87%, 60%)"},function:{color:"hsl(221, 87%, 60%)"},url:{color:"hsl(198, 99%, 37%)"},"attr-value > .token.punctuation.attr-equals":{color:"hsl(230, 8%, 24%)"},"special-attr > .token.attr-value > .token.value.css":{color:"hsl(230, 8%, 24%)"},".language-css .token.selector":{color:"hsl(5, 74%, 59%)"},".language-css .token.property":{color:"hsl(230, 8%, 24%)"},".language-css .token.function":{color:"hsl(198, 99%, 37%)"},".language-css .token.url > .token.function":{color:"hsl(198, 99%, 37%)"},".language-css .token.url > .token.string.url":{color:"hsl(119, 34%, 47%)"},".language-css .token.important":{color:"hsl(301, 63%, 40%)"},".language-css .token.atrule .token.rule":{color:"hsl(301, 63%, 40%)"},".language-javascript .token.operator":{color:"hsl(301, 63%, 40%)"},".language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation":{color:"hsl(344, 84%, 43%)"},".language-json .token.operator":{color:"hsl(230, 8%, 24%)"},".language-json .token.null.keyword":{color:"hsl(35, 99%, 36%)"},".language-markdown .token.url":{color:"hsl(230, 8%, 24%)"},".language-markdown .token.url > .token.operator":{color:"hsl(230, 8%, 24%)"},".language-markdown .token.url-reference.url > .token.string":{color:"hsl(230, 8%, 24%)"},".language-markdown .token.url > .token.content":{color:"hsl(221, 87%, 60%)"},".language-markdown .token.url > .token.url":{color:"hsl(198, 99%, 37%)"},".language-markdown .token.url-reference.url":{color:"hsl(198, 99%, 37%)"},".language-markdown .token.blockquote.punctuation":{color:"hsl(230, 4%, 64%)",fontStyle:"italic"},".language-markdown .token.hr.punctuation":{color:"hsl(230, 4%, 64%)",fontStyle:"italic"},".language-markdown .token.code-snippet":{color:"hsl(119, 34%, 47%)"},".language-markdown .token.bold .token.content":{color:"hsl(35, 99%, 36%)"},".language-markdown .token.italic .token.content":{color:"hsl(301, 63%, 40%)"},".language-markdown .token.strike .token.content":{color:"hsl(5, 74%, 59%)"},".language-markdown .token.strike .token.punctuation":{color:"hsl(5, 74%, 59%)"},".language-markdown .token.list.punctuation":{color:"hsl(5, 74%, 59%)"},".language-markdown .token.title.important > .token.punctuation":{color:"hsl(5, 74%, 59%)"},bold:{fontWeight:"bold"},italic:{fontStyle:"italic"},namespace:{Opacity:"0.8"},"token.tab:not(:empty):before":{color:"hsla(230, 8%, 24%, 0.2)"},"token.cr:before":{color:"hsla(230, 8%, 24%, 0.2)"},"token.lf:before":{color:"hsla(230, 8%, 24%, 0.2)"},"token.space:before":{color:"hsla(230, 8%, 24%, 0.2)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item":{marginRight:"0.4em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button":{background:"hsl(230, 1%, 90%)",color:"hsl(230, 6%, 44%)",padding:"0.1em 0.4em",borderRadius:"0.3em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a":{background:"hsl(230, 1%, 90%)",color:"hsl(230, 6%, 44%)",padding:"0.1em 0.4em",borderRadius:"0.3em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span":{background:"hsl(230, 1%, 90%)",color:"hsl(230, 6%, 44%)",padding:"0.1em 0.4em",borderRadius:"0.3em"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover":{background:"hsl(230, 1%, 78%)",color:"hsl(230, 8%, 24%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus":{background:"hsl(230, 1%, 78%)",color:"hsl(230, 8%, 24%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover":{background:"hsl(230, 1%, 78%)",color:"hsl(230, 8%, 24%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus":{background:"hsl(230, 1%, 78%)",color:"hsl(230, 8%, 24%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover":{background:"hsl(230, 1%, 78%)",color:"hsl(230, 8%, 24%)"},"div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus":{background:"hsl(230, 1%, 78%)",color:"hsl(230, 8%, 24%)"},".line-highlight.line-highlight":{background:"hsla(230, 8%, 24%, 0.05)"},".line-highlight.line-highlight:before":{background:"hsl(230, 1%, 90%)",color:"hsl(230, 8%, 24%)",padding:"0.1em 0.6em",borderRadius:"0.3em",boxShadow:"0 2px 0 0 rgba(0, 0, 0, 0.2)"},".line-highlight.line-highlight[data-end]:after":{background:"hsl(230, 1%, 90%)",color:"hsl(230, 8%, 24%)",padding:"0.1em 0.6em",borderRadius:"0.3em",boxShadow:"0 2px 0 0 rgba(0, 0, 0, 0.2)"},"pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before":{backgroundColor:"hsla(230, 8%, 24%, 0.05)"},".line-numbers.line-numbers .line-numbers-rows":{borderRightColor:"hsla(230, 8%, 24%, 0.2)"},".command-line .command-line-prompt":{borderRightColor:"hsla(230, 8%, 24%, 0.2)"},".line-numbers .line-numbers-rows > span:before":{color:"hsl(230, 1%, 62%)"},".command-line .command-line-prompt > span:before":{color:"hsl(230, 1%, 62%)"},".rainbow-braces .token.token.punctuation.brace-level-1":{color:"hsl(5, 74%, 59%)"},".rainbow-braces .token.token.punctuation.brace-level-5":{color:"hsl(5, 74%, 59%)"},".rainbow-braces .token.token.punctuation.brace-level-9":{color:"hsl(5, 74%, 59%)"},".rainbow-braces .token.token.punctuation.brace-level-2":{color:"hsl(119, 34%, 47%)"},".rainbow-braces .token.token.punctuation.brace-level-6":{color:"hsl(119, 34%, 47%)"},".rainbow-braces .token.token.punctuation.brace-level-10":{color:"hsl(119, 34%, 47%)"},".rainbow-braces .token.token.punctuation.brace-level-3":{color:"hsl(221, 87%, 60%)"},".rainbow-braces .token.token.punctuation.brace-level-7":{color:"hsl(221, 87%, 60%)"},".rainbow-braces .token.token.punctuation.brace-level-11":{color:"hsl(221, 87%, 60%)"},".rainbow-braces .token.token.punctuation.brace-level-4":{color:"hsl(301, 63%, 40%)"},".rainbow-braces .token.token.punctuation.brace-level-8":{color:"hsl(301, 63%, 40%)"},".rainbow-braces .token.token.punctuation.brace-level-12":{color:"hsl(301, 63%, 40%)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix)":{backgroundColor:"hsla(353, 100%, 66%, 0.15)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix)":{backgroundColor:"hsla(353, 100%, 66%, 0.15)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix)::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.deleted:not(.prefix) *::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix)::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre > code.diff-highlight .token.token.deleted:not(.prefix) *::selection":{backgroundColor:"hsla(353, 95%, 66%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix)":{backgroundColor:"hsla(137, 100%, 55%, 0.15)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix)":{backgroundColor:"hsla(137, 100%, 55%, 0.15)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix)::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre.diff-highlight > code .token.token.inserted:not(.prefix) *::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix)::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},"pre > code.diff-highlight .token.token.inserted:not(.prefix) *::selection":{backgroundColor:"hsla(135, 73%, 55%, 0.25)"},".prism-previewer.prism-previewer:before":{borderColor:"hsl(0, 0, 95%)"},".prism-previewer-gradient.prism-previewer-gradient div":{borderColor:"hsl(0, 0, 95%)",borderRadius:"0.3em"},".prism-previewer-color.prism-previewer-color:before":{borderRadius:"0.3em"},".prism-previewer-easing.prism-previewer-easing:before":{borderRadius:"0.3em"},".prism-previewer.prism-previewer:after":{borderTopColor:"hsl(0, 0, 95%)"},".prism-previewer-flipped.prism-previewer-flipped.after":{borderBottomColor:"hsl(0, 0, 95%)"},".prism-previewer-angle.prism-previewer-angle:before":{background:"hsl(0, 0%, 100%)"},".prism-previewer-time.prism-previewer-time:before":{background:"hsl(0, 0%, 100%)"},".prism-previewer-easing.prism-previewer-easing":{background:"hsl(0, 0%, 100%)"},".prism-previewer-angle.prism-previewer-angle circle":{stroke:"hsl(230, 8%, 24%)",strokeOpacity:"1"},".prism-previewer-time.prism-previewer-time circle":{stroke:"hsl(230, 8%, 24%)",strokeOpacity:"1"},".prism-previewer-easing.prism-previewer-easing circle":{stroke:"hsl(230, 8%, 24%)",fill:"transparent"},".prism-previewer-easing.prism-previewer-easing path":{stroke:"hsl(230, 8%, 24%)"},".prism-previewer-easing.prism-previewer-easing line":{stroke:"hsl(230, 8%, 24%)"}},Vd=o=>{const m=document.createElement("textarea");return m.innerHTML=o,m.value};function bv(o){const m=new RegExp("((?<!\\\\)\\$\\$([\\s\\S]*?)(?<!\\\\)\\$\\$|\\\\\\[([\\s\\S]*?)\\\\\\])","g");let c=o.replace(m,(S,T,y,z)=>{let x=y||z;x.endsWith("\\$")&&(x+=" ");let w=x.replace(/<br\s*\/?>/gi,`
`);return w=Vd(w),y!==void 0?`$$${w}$$`:`\\[${w}\\]`});const g=new RegExp("((?<!\\\\)\\$((?:[^$\\n\\\\]|\\\\.)+?)(?<!\\\\)\\$(?!\\$)|\\\\\\(([^)]*?)\\\\\\))","g");return c=c.replace(g,(S,T,y,z)=>{let w=(y||z).replace(/<br\s*\/?>/gi," ");return w=w.replace(/\\\$/g,"🪷"),w=Vd(w),y!==void 0?`$${w}$`:`\\(${w}\\)`}),c}const yv=()=>({tagNames:["p","br","strong","em","u","s","del","ins","h1","h2","h3","h4","h5","h6","ul","ol","li","dl","dt","dd","blockquote","pre","code","hr","table","thead","tbody","tr","th","td","a","img","div","span","section","article","aside","nav","header","footer","main","figure","figcaption","mark","small","sub","sup","kbd","samp","var"],attributes:{"*":["className","id","style"],a:["href","title","target","rel"],img:["src","alt","title","width","height"],th:["align","colspan","rowspan"],td:["align","colspan","rowspan"],ol:["start","type"],li:["value"]},protocols:{href:["http","https","mailto","tel"],src:["http","https","data"]},strip:["script","style","iframe","object","embed"],clobberPrefix:"user-content-"}),uc=({children:o,className:m="",allowHtml:c=!1,sanitize:g=!1,customSanitizeSchema:S=null})=>{const{colorScheme:T}=v0(),y=T==="dark"?pv:vv,z=N=>N.replace(/\\:/g,":"),x=N=>N.replace(/<br\s*\/?>/gi,`
`).replace(/<div\s*\/?>/gi,`
`).replace(/<\/div>/gi,`
`),w=(()=>{let N=c?o:Vd(o);return N=x(N),N=z(N),N=bv(N),N})(),q={borderRadius:"4px",padding:"8px",marginTop:"0.5em",marginBottom:"0.5em"},M=()=>{const N=[tm];if(c&&(N.push(em),g)){const F=S||yv();N.push([nm,F])}return N};return nt.jsx(N1,{className:`${m} markdown-content`,style:{fontSize:"24px",lineHeight:"1.4"},children:nt.jsx($1,{remarkPlugins:[I1,mv],rehypePlugins:M(),components:{span:({className:N,children:F,...Z})=>(typeof F=="string"&&F.includes("🪷")&&(F=F.replace(/🪷/g,"$")),nt.jsx("span",{className:N,...Z,children:F})),pre:({children:N,...F})=>nt.jsx("div",{...F,children:N}),code:({className:N,children:F,...Z})=>{const I=/language-(\w+)/.exec(N||""),et=String(Vd(F)).replace(/^\n/,"").replace(/\n$/,"");return nt.jsx(P1,{style:y,language:I&&I[1]||"text",PreTag:I||String(F).includes(`
`)?"div":"span",customStyle:{...q},codeTagProps:{style:{lineHeight:"inherit",fontSize:"inherit",backgroundColor:"inherit",padding:"0"}},wrapLines:!0,...Z,children:et})},table:({children:N,...F})=>nt.jsx(tc,{striped:!0,highlightOnHover:!0,withTableBorder:!0,withColumnBorders:!0,style:{marginTop:"1em",marginBottom:"1em"},...F,children:N}),thead:({children:N,...F})=>nt.jsx(tc.Thead,{...F,children:N}),tbody:({children:N,...F})=>nt.jsx(tc.Tbody,{...F,children:N}),tr:({children:N,...F})=>nt.jsx(tc.Tr,{...F,children:N}),th:({children:N,...F})=>nt.jsx(tc.Th,{...F,children:N}),td:({children:N,...F})=>nt.jsx(tc.Td,{...F,children:N})},children:w})})};function h1({frontNode:o,backNode:m,extraNode:c,contentVersion:g,colors:S}){const[T,y]=Ft.useState({front:"Loading...",back:"Loading...",extra:"Loading..."}),z=x=>w=>{const[q,M]=x.split("."),N=parseInt(M,10);return{border:`1px solid ${w.colors[q][N]}`}};return Ft.useEffect(()=>{y({front:o?.innerHTML.trim()||"",back:m?.innerHTML.trim()||"",extra:c?.innerHTML.trim()||""})},[o,m,c,g]),nt.jsx("div",{children:nt.jsxs(L0,{gap:"md",children:[nt.jsxs("div",{children:[nt.jsx(lc,{fw:600,size:"md",mb:"xs",c:"dimmed",children:"FRONT"}),nt.jsx(ic,{p:"md",bg:S.front.bg,style:x=>({...z(S.front.border)(x),overflowX:"auto"}),radius:"sm",children:nt.jsx(uc,{allowHtml:!0,children:T.front})})]}),T.back&&nt.jsxs("div",{children:[nt.jsx(lc,{fw:600,size:"md",mb:"xs",c:"dimmed",children:"BACK"}),nt.jsx(ic,{p:"md",bg:S.back.bg,style:x=>({...z(S.back.border)(x),overflowX:"auto"}),radius:"sm",children:nt.jsx(uc,{allowHtml:!0,children:T.back})})]}),T.extra&&nt.jsxs("div",{children:[nt.jsx(lc,{fw:600,size:"md",mb:"xs",c:"dimmed",children:"EXTRA"}),nt.jsx(ic,{p:"md",bg:S.extra.bg,style:x=>({...z(S.extra.border)(x),overflowX:"auto"}),radius:"sm",children:nt.jsx(uc,{allowHtml:!0,children:T.extra})})]})]})})}function Sv({spanElement:o,label:m,text:c}){const[g,S]=Ft.useState(!1);Ft.useEffect(()=>{S(!1)},[c]);const T=y=>{const z=y.getAttribute("data-cloze"),x=y.textContent;y.setAttribute("data-cloze",x),y.textContent=z};return nt.jsx(q1,{checked:g,onChange:y=>{S(y.currentTarget.checked),T(o)},label:m})}function Kd(o){const m=document.implementation.createHTMLDocument("");return m.body.innerHTML=o,m.querySelectorAll("span.cloze, span.cloze-inactive").forEach(S=>{S.outerHTML=S.innerHTML}),m.body.innerHTML}function _v(o){const m=document.implementation.createHTMLDocument("");return m.body.innerHTML=o,m.querySelectorAll("span.cloze").forEach(g=>{g.innerHTML=g.innerHTML.replace(/^(\s*(\r?\n|<br\s*\/?>))*/gi,"")}),m.body.innerHTML}function xv(o){const m=/\`\`\`(\w*)([\s\S]*?)\`\`\`/g;let c=o.replace(m,(S,T,y)=>{let z=y.replace(/<br\s*\/?>/gi,`
`);return z=Kd(z),`
\`\`\`${T}${z}\`\`\``});const g=/`([^`\n]+)`/g;return c=c.replace(g,(S,T)=>{let y=T.replace(/<br\s*\/?>/gi,`
`);return y=Kd(y),`\`${y}\``}),c}function Av(o){const m=/(\$\$([\s\S]*?)\$\$|\\\[([\s\S]*?)\\\])/g;let c=o.replace(m,(S,T,y,z)=>{let w=(y||z).replace(/<br\s*\/?>/gi,`
`);return w=Kd(w),y!==void 0?`$$${w}$$`:`\\[${w}\\]`});const g=new RegExp("((?<!\\$)\\$([^$\\n]+)\\$(?!\\$)|\\\\\\(([^)]*?)\\\\\\))","g");return c=c.replace(g,(S,T,y,z)=>{let w=(y||z).replace(/<br\s*\/?>/gi," ");return w=Kd(w),y!==void 0?`$${w}$`:`\\(${w}\\)`}),c}function d1({frontNode:o,backNode:m,extraNode:c,contentVersion:g,colors:S}){const T=Ft.useRef(null),[y,z]=Ft.useState({front:"Loading...",back:"Loading...",extra:"Loading..."}),[x,w]=Ft.useState(0),q=I=>et=>{const[ut,dt]=I.split("."),it=parseInt(dt,10);return{border:`1px solid ${et.colors[ut][it]}`}},M=document.getElementById("front-card-cloze"),N=Array.from(M?M.querySelectorAll("span.cloze"):[]),F=I=>{if(!I)return"";const et=xv(I.innerHTML.trim()||""),ut=Av(et);return _v(ut)};function Z(I,et){return[I,et].sort().join("")}return Ft.useEffect(()=>{y.back!==F(m)&&w(I=>I+1),z({front:F(o),back:F(m),extra:F(c)})},[o,m,c,g]),nt.jsx("div",{ref:T,children:nt.jsxs(L0,{gap:"md",children:[y.front&&nt.jsxs("div",{children:[nt.jsx(lc,{fw:600,size:"lg",mb:"xs",c:"dimmed",children:"FRONT"}),nt.jsx(jd,{mb:"xs",children:N.length>0&&N.map((I,et)=>nt.jsx(Sv,{spanElement:I,label:`Cloze ${et+1}`,text:Z(I.getAttribute("data-cloze"),I.textContent)},et))}),nt.jsx(ic,{p:"md",bg:S.front.bg,style:I=>({...q(S.front.border)(I),maxHeight:"90vh",overflowY:"auto",overflowX:"auto"}),radius:"sm",children:nt.jsx(uc,{allowHtml:!0,children:y.front})})]}),y.back&&nt.jsxs("div",{children:[nt.jsx(lc,{fw:600,size:"lg",mb:"xs",c:"dimmed",children:"BACK"}),nt.jsx(ic,{p:"md",bg:S.back.bg,style:I=>({...q(S.back.border)(I),overflowX:"auto"}),radius:"sm",children:nt.jsx(uc,{allowHtml:!0,children:y.back})})]}),y.extra&&nt.jsxs("div",{children:[nt.jsx(lc,{fw:600,size:"lg",mb:"xs",c:"dimmed",children:"EXTRA"}),nt.jsx(ic,{p:"md",bg:S.extra.bg,style:I=>({...q(S.extra.border)(I),overflowX:"auto"}),radius:"sm",children:nt.jsx(uc,{allowHtml:!0,children:y.extra})})]})]})})}function Tv({tags:o}){return nt.jsx(nt.Fragment,{children:o&&o.length>0&&nt.jsx(jd,{mb:"sm",children:o.map((m,c)=>nt.jsx(H0,{variant:"light",color:"gray",size:"lg",children:m},c))})})}function wv({difficulty:o}){return nt.jsx(nt.Fragment,{children:nt.jsx(jd,{mb:"sm",children:o&&nt.jsx(H0,{variant:"light",color:"orange",size:"lg",children:o})})})}function g1({tags:o,difficulty:m}){return nt.jsxs(jd,{justify:"space-between",align:"flex-start",wrap:"wrap",children:[nt.jsx(wv,{difficulty:m}),nt.jsx(Tv,{tags:o})]})}function kv(){const{colorScheme:o}=v0(),m=["front-card-basic","back-card-basic","extra-card-basic"],c=["front-card-cloze","back-card-cloze","extra-card-cloze"],g=["tags-card","tags-card-basic","tags-card-cloze"],S=["difficulty-card","difficulty-card-basic","difficulty-card-cloze"],[T,y]=Ft.useState({front:null,back:null,extra:null}),[z,x]=Ft.useState({front:null,back:null,extra:null,contentVersion:0}),[w,q]=Ft.useState([]),[M,N]=Ft.useState(null),F=St=>St?St.trim().split(/\s+/).filter(ue=>ue.length>0):[],[Z,I]=Ft.useState(!1),[et,ut]=Ft.useState(0);Ft.useEffect(()=>{Math.random()<=.002&&(I(!0),setTimeout(()=>{I(!1)},7e3))},[et]),Ft.useEffect(()=>{const St=m.map(Qt=>document.getElementById(Qt)),ue=c.map(Qt=>document.getElementById(Qt)),K=g.map(Qt=>document.getElementById(Qt)).filter(Qt=>Qt!==null)[0],Re=S.map(Qt=>document.getElementById(Qt)).filter(Qt=>Qt!==null)[0];y({front:St[0],back:St[1],extra:St[2]}),x({front:ue[0],back:ue[1],extra:ue[2],contentVersion:Date.now()}),q(F(K?K.innerText:"")),N(Re?Re.innerText.trim():null),ut(Qt=>Qt+1),new MutationObserver(Qt=>{const en=[...m,...c];if(!Qt.some(({target:bt,addedNodes:ae,removedNodes:fe})=>{const Zt=Ue=>{if(!Ue)return!1;let we=Ue;for(;we;){if(we.id&&en.includes(we.id))return!0;if(we=we.parentNode,we&&we.nodeType===Node.DOCUMENT_NODE)break}return!1};return[bt,...Array.from(ae),...Array.from(fe)].some(Zt)}))return;const X=m.map(bt=>document.getElementById(bt)),$=c.map(bt=>document.getElementById(bt)),ft=g.map(bt=>document.getElementById(bt)).filter(bt=>bt!==null)[0],It=S.map(bt=>document.getElementById(bt)).filter(bt=>bt!==null)[0];y({front:X[0],back:X[1],extra:X[2]}),x({front:$[0],back:$[1],extra:$[2],contentVersion:Date.now()}),q(F(ft?ft.innerText:"")),N(It?It.innerText.trim():null),ut(bt=>bt+1)}).observe(document.body,{childList:!0,subtree:!0,characterData:!0})},[]);const[dt,it]=Ft.useState(!1),[Ut,Ct]=Ft.useState(!1);Ft.useEffect(()=>{const St=T.front||T.back||T.extra,ue=z.front||z.back||z.extra;it(St),Ct(ue)},[T,z]);const $t=(()=>{const St=o==="dark";return{front:{bg:St?"dark.6":"gray.1",border:St?"dark.4":"gray.3"},back:{bg:St?"dark.5":"blue.0",border:St?"blue.8":"blue.3"},extra:{bg:St?"dark.4":"yellow.1",border:St?"yellow.8":"yellow.3"}}})();return nt.jsxs(N0,{shadow:"sm",padding:"md",radius:"md",withBorder:!0,children:[nt.jsx(g1,{tags:w,difficulty:M}),dt&&nt.jsx(h1,{contentVersion:T.contentVersion,colors:$t,frontNode:T.front,backNode:T.back,extraNode:T.extra}),Ut&&nt.jsx(d1,{contentVersion:z.contentVersion,colors:$t,frontNode:z.front,backNode:z.back,extraNode:z.extra})]})}var cs={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */var zv=cs.exports,B0;function Ev(){return B0||(B0=1,function(o,m){(function(){var c,g="4.17.21",S=200,T="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",y="Expected a function",z="Invalid `variable` option passed into `_.template`",x="__lodash_hash_undefined__",w=500,q="__lodash_placeholder__",M=1,N=2,F=4,Z=1,I=2,et=1,ut=2,dt=4,it=8,Ut=16,Ct=32,Lt=64,$t=128,St=256,ue=512,K=30,Re="...",Et=800,Qt=16,en=1,ul=2,X=3,$=1/0,ft=9007199254740991,It=17976931348623157e292,bt=NaN,ae=**********,fe=ae-1,Zt=ae>>>1,Te=[["ary",$t],["bind",et],["bindKey",ut],["curry",it],["curryRight",Ut],["flip",ue],["partial",Ct],["partialRight",Lt],["rearg",St]],Ue="[object Arguments]",we="[object Array]",br="[object AsyncFunction]",Gl="[object Boolean]",al="[object Date]",fc="[object DOMException]",Si="[object Error]",Fi="[object Function]",yr="[object GeneratorFunction]",bn="[object Map]",ta="[object Number]",Jd="[object Null]",Be="[object Object]",ds="[object Promise]",gs="[object Proxy]",$i="[object RegExp]",nn="[object Set]",ea="[object String]",na="[object Symbol]",Wd="[object Undefined]",la="[object WeakMap]",ia="[object WeakSet]",Xe="[object ArrayBuffer]",yn="[object DataView]",Je="[object Float32Array]",cc="[object Float64Array]",oc="[object Int8Array]",sc="[object Int16Array]",Ii="[object Int32Array]",Pi="[object Uint8Array]",Sl="[object Uint8ClampedArray]",tu="[object Uint16Array]",_i="[object Uint32Array]",Fd=/\b__p \+= '';/g,ms=/\b(__p \+=) '' \+/g,ps=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Sr=/&(?:amp|lt|gt|quot|#39);/g,eu=/[&<>"']/g,$d=RegExp(Sr.source),vs=RegExp(eu.source),bs=/<%-([\s\S]+?)%>/g,hc=/<%([\s\S]+?)%>/g,_r=/<%=([\s\S]+?)%>/g,ys=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Id=/^\w*$/,Yl=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Le=/[\\^$.*+?()[\]{}|]/g,ln=RegExp(Le.source),Xl=/^\s+/,dc=/\s/,Pd=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,tg=/\{\n\/\* \[wrapped with (.+)\] \*/,Ss=/,? & /,ua=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,gc=/[()=,{}\[\]\/\s]/,nu=/\\(\\)?/g,lu=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,iu=/\w*$/,uu=/^[-+]0x[0-9a-f]+$/i,He=/^0b[01]+$/i,_s=/^\[object .+?Constructor\]$/,xs=/^0o[0-7]+$/i,xi=/^(?:0|[1-9]\d*)$/,au=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,xr=/($^)/,As=/['\n\r\u2028\u2029\\]/g,aa="\\ud800-\\udfff",eg="\\u0300-\\u036f",Ar="\\ufe20-\\ufe2f",Tr="\\u20d0-\\u20ff",rl=eg+Ar+Tr,wr="\\u2700-\\u27bf",mc="a-z\\xdf-\\xf6\\xf8-\\xff",ru="\\xac\\xb1\\xd7\\xf7",pc="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",vc="\\u2000-\\u206f",ng=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",bc="A-Z\\xc0-\\xd6\\xd8-\\xde",Sn="\\ufe0e\\ufe0f",yc=ru+pc+vc+ng,Sc="['’]",kr="["+aa+"]",_c="["+yc+"]",Ai="["+rl+"]",Ts="\\d+",On="["+wr+"]",zr="["+mc+"]",xc="[^"+aa+yc+Ts+wr+mc+bc+"]",ra="\\ud83c[\\udffb-\\udfff]",fu="(?:"+Ai+"|"+ra+")",Ac="[^"+aa+"]",Er="(?:\\ud83c[\\udde6-\\uddff]){2}",Ql="[\\ud800-\\udbff][\\udc00-\\udfff]",cu="["+bc+"]",Tc="\\u200d",wc="(?:"+zr+"|"+xc+")",kc="(?:"+cu+"|"+xc+")",ws="(?:"+Sc+"(?:d|ll|m|re|s|t|ve))?",ks="(?:"+Sc+"(?:D|LL|M|RE|S|T|VE))?",fa=fu+"?",Mr="["+Sn+"]?",zc="(?:"+Tc+"(?:"+[Ac,Er,Ql].join("|")+")"+Mr+fa+")*",ou="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",su="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ec=Mr+fa+zc,Mc="(?:"+[On,Er,Ql].join("|")+")"+Ec,zs="(?:"+[Ac+Ai+"?",Ai,Er,Ql,kr].join("|")+")",ca=RegExp(Sc,"g"),_l=RegExp(Ai,"g"),oa=RegExp(ra+"(?="+ra+")|"+zs+Ec,"g"),sa=RegExp([cu+"?"+zr+"+"+ws+"(?="+[_c,cu,"$"].join("|")+")",kc+"+"+ks+"(?="+[_c,cu+wc,"$"].join("|")+")",cu+"?"+wc+"+"+ws,cu+"+"+ks,su,ou,Ts,Mc].join("|"),"g"),Zl=RegExp("["+Tc+aa+rl+Sn+"]"),Dc=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Dr=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Es=-1,Pt={};Pt[Je]=Pt[cc]=Pt[oc]=Pt[sc]=Pt[Ii]=Pt[Pi]=Pt[Sl]=Pt[tu]=Pt[_i]=!0,Pt[Ue]=Pt[we]=Pt[Xe]=Pt[Gl]=Pt[yn]=Pt[al]=Pt[Si]=Pt[Fi]=Pt[bn]=Pt[ta]=Pt[Be]=Pt[$i]=Pt[nn]=Pt[ea]=Pt[la]=!1;var Kt={};Kt[Ue]=Kt[we]=Kt[Xe]=Kt[yn]=Kt[Gl]=Kt[al]=Kt[Je]=Kt[cc]=Kt[oc]=Kt[sc]=Kt[Ii]=Kt[bn]=Kt[ta]=Kt[Be]=Kt[$i]=Kt[nn]=Kt[ea]=Kt[na]=Kt[Pi]=Kt[Sl]=Kt[tu]=Kt[_i]=!0,Kt[Si]=Kt[Fi]=Kt[la]=!1;var Ms={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},un={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ti={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Cr={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ha=parseFloat,lg=parseInt,Or=typeof Yd=="object"&&Yd&&Yd.Object===Object&&Yd,Cc=typeof self=="object"&&self&&self.Object===Object&&self,_e=Or||Cc||Function("return this")(),hu=m&&!m.nodeType&&m,Vl=hu&&!0&&o&&!o.nodeType&&o,Ds=Vl&&Vl.exports===hu,Oc=Ds&&Or.process,Rn=function(){try{var D=Vl&&Vl.require&&Vl.require("util").types;return D||Oc&&Oc.binding&&Oc.binding("util")}catch{}}(),Rr=Rn&&Rn.isArrayBuffer,Cs=Rn&&Rn.isDate,Os=Rn&&Rn.isMap,Rs=Rn&&Rn.isRegExp,Us=Rn&&Rn.isSet,Bs=Rn&&Rn.isTypedArray;function an(D,G,H){switch(H.length){case 0:return D.call(G);case 1:return D.call(G,H[0]);case 2:return D.call(G,H[0],H[1]);case 3:return D.call(G,H[0],H[1],H[2])}return D.apply(G,H)}function ig(D,G,H,P){for(var gt=-1,Ht=D==null?0:D.length;++gt<Ht;){var xe=D[gt];G(P,xe,H(xe),D)}return P}function Un(D,G){for(var H=-1,P=D==null?0:D.length;++H<P&&G(D[H],H,D)!==!1;);return D}function ug(D,G){for(var H=D==null?0:D.length;H--&&G(D[H],H,D)!==!1;);return D}function Ls(D,G){for(var H=-1,P=D==null?0:D.length;++H<P;)if(!G(D[H],H,D))return!1;return!0}function fl(D,G){for(var H=-1,P=D==null?0:D.length,gt=0,Ht=[];++H<P;){var xe=D[H];G(xe,H,D)&&(Ht[gt++]=xe)}return Ht}function Ur(D,G){var H=D==null?0:D.length;return!!H&&du(D,G,0)>-1}function Rc(D,G,H){for(var P=-1,gt=D==null?0:D.length;++P<gt;)if(H(G,D[P]))return!0;return!1}function se(D,G){for(var H=-1,P=D==null?0:D.length,gt=Array(P);++H<P;)gt[H]=G(D[H],H,D);return gt}function xl(D,G){for(var H=-1,P=G.length,gt=D.length;++H<P;)D[gt+H]=G[H];return D}function Uc(D,G,H,P){var gt=-1,Ht=D==null?0:D.length;for(P&&Ht&&(H=D[++gt]);++gt<Ht;)H=G(H,D[gt],gt,D);return H}function ag(D,G,H,P){var gt=D==null?0:D.length;for(P&&gt&&(H=D[--gt]);gt--;)H=G(H,D[gt],gt,D);return H}function Bc(D,G){for(var H=-1,P=D==null?0:D.length;++H<P;)if(G(D[H],H,D))return!0;return!1}var rg=Lr("length");function fg(D){return D.split("")}function cg(D){return D.match(ua)||[]}function Hs(D,G,H){var P;return H(D,function(gt,Ht,xe){if(G(gt,Ht,xe))return P=Ht,!1}),P}function Br(D,G,H,P){for(var gt=D.length,Ht=H+(P?1:-1);P?Ht--:++Ht<gt;)if(G(D[Ht],Ht,D))return Ht;return-1}function du(D,G,H){return G===G?dg(D,G,H):Br(D,gu,H)}function Lc(D,G,H,P){for(var gt=H-1,Ht=D.length;++gt<Ht;)if(P(D[gt],G))return gt;return-1}function gu(D){return D!==D}function Ns(D,G){var H=D==null?0:D.length;return H?Nr(D,G)/H:bt}function Lr(D){return function(G){return G==null?c:G[D]}}function Hr(D){return function(G){return D==null?c:D[G]}}function Hc(D,G,H,P,gt){return gt(D,function(Ht,xe,Nt){H=P?(P=!1,Ht):G(H,Ht,xe,Nt)}),H}function qs(D,G){var H=D.length;for(D.sort(G);H--;)D[H]=D[H].value;return D}function Nr(D,G){for(var H,P=-1,gt=D.length;++P<gt;){var Ht=G(D[P]);Ht!==c&&(H=H===c?Ht:H+Ht)}return H}function Kl(D,G){for(var H=-1,P=Array(D);++H<D;)P[H]=G(H);return P}function og(D,G){return se(G,function(H){return[H,D[H]]})}function Gs(D){return D&&D.slice(0,qc(D)+1).replace(Xl,"")}function _n(D){return function(G){return D(G)}}function qr(D,G){return se(G,function(H){return D[H]})}function mu(D,G){return D.has(G)}function pu(D,G){for(var H=-1,P=D.length;++H<P&&du(G,D[H],0)>-1;);return H}function vu(D,G){for(var H=D.length;H--&&du(G,D[H],0)>-1;);return H}function sg(D,G){for(var H=D.length,P=0;H--;)D[H]===G&&++P;return P}var Gr=Hr(Ms),Ys=Hr(un);function Xs(D){return"\\"+Cr[D]}function Nc(D,G){return D==null?c:D[G]}function jl(D){return Zl.test(D)}function Qs(D){return Dc.test(D)}function Zs(D){for(var G,H=[];!(G=D.next()).done;)H.push(G.value);return H}function Yr(D){var G=-1,H=Array(D.size);return D.forEach(function(P,gt){H[++G]=[gt,P]}),H}function Vs(D,G){return function(H){return D(G(H))}}function Jl(D,G){for(var H=-1,P=D.length,gt=0,Ht=[];++H<P;){var xe=D[H];(xe===G||xe===q)&&(D[H]=q,Ht[gt++]=H)}return Ht}function Xr(D){var G=-1,H=Array(D.size);return D.forEach(function(P){H[++G]=P}),H}function hg(D){var G=-1,H=Array(D.size);return D.forEach(function(P){H[++G]=[P,P]}),H}function dg(D,G,H){for(var P=H-1,gt=D.length;++P<gt;)if(D[P]===G)return P;return-1}function xn(D,G,H){for(var P=H+1;P--;)if(D[P]===G)return P;return P}function cl(D){return jl(D)?js(D):rg(D)}function Bn(D){return jl(D)?Gc(D):fg(D)}function qc(D){for(var G=D.length;G--&&dc.test(D.charAt(G)););return G}var Ks=Hr(Ti);function js(D){for(var G=oa.lastIndex=0;oa.test(D);)++G;return G}function Gc(D){return D.match(oa)||[]}function gg(D){return D.match(sa)||[]}var bu=function D(G){G=G==null?_e:Wl.defaults(_e.Object(),G,Wl.pick(_e,Dr));var H=G.Array,P=G.Date,gt=G.Error,Ht=G.Function,xe=G.Math,Nt=G.Object,Qr=G.RegExp,Js=G.String,An=G.TypeError,Zr=H.prototype,mg=Ht.prototype,yu=Nt.prototype,da=G["__core-js_shared__"],ga=mg.toString,Yt=yu.hasOwnProperty,jn=0,Yc=function(){var l=/[^.]+$/.exec(da&&da.keys&&da.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),We=yu.toString,Ln=ga.call(Nt),Su=_e._,Xc=Qr("^"+ga.call(Yt).replace(Le,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),wi=Ds?G.Buffer:c,Jn=G.Symbol,_u=G.Uint8Array,ki=wi?wi.allocUnsafe:c,ma=Vs(Nt.getPrototypeOf,Nt),pa=Nt.create,zi=yu.propertyIsEnumerable,Vr=Zr.splice,rn=Jn?Jn.isConcatSpreadable:c,Ei=Jn?Jn.iterator:c,Fe=Jn?Jn.toStringTag:c,va=function(){try{var l=ii(Nt,"defineProperty");return l({},"",{}),l}catch{}}(),Kr=G.clearTimeout!==_e.clearTimeout&&G.clearTimeout,Mi=P&&P.now!==_e.Date.now&&P.now,Qc=G.setTimeout!==_e.setTimeout&&G.setTimeout,xu=xe.ceil,Al=xe.floor,Fl=Nt.getOwnPropertySymbols,jr=wi?wi.isBuffer:c,ba=G.isFinite,Hn=Zr.join,Nn=Vs(Nt.keys,Nt),ce=xe.max,he=xe.min,Tl=P.now,Di=G.parseInt,Zc=xe.random,Vc=Zr.reverse,ya=ii(G,"DataView"),ke=ii(G,"Map"),de=ii(G,"Promise"),Mt=ii(G,"Set"),Wn=ii(G,"WeakMap"),fn=ii(Nt,"create"),Au=Wn&&new Wn,qn={},Ws=Rl(ya),Fs=Rl(ke),Sa=Rl(de),_a=Rl(Mt),$s=Rl(Wn),$l=Jn?Jn.prototype:c,Ci=$l?$l.valueOf:c,Il=$l?$l.toString:c;function p(l){if(pe(l)&&!vt(l)&&!(l instanceof At)){if(l instanceof cn)return l;if(Yt.call(l,"__wrapped__"))return Ee(l)}return new cn(l)}var Tn=function(){function l(){}return function(i){if(!me(i))return{};if(pa)return pa(i);l.prototype=i;var r=new l;return l.prototype=c,r}}();function Gn(){}function cn(l,i){this.__wrapped__=l,this.__actions__=[],this.__chain__=!!i,this.__index__=0,this.__values__=c}p.templateSettings={escape:bs,evaluate:hc,interpolate:_r,variable:"",imports:{_:p}},p.prototype=Gn.prototype,p.prototype.constructor=p,cn.prototype=Tn(Gn.prototype),cn.prototype.constructor=cn;function At(l){this.__wrapped__=l,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ae,this.__views__=[]}function xa(){var l=new At(this.__wrapped__);return l.__actions__=ze(this.__actions__),l.__dir__=this.__dir__,l.__filtered__=this.__filtered__,l.__iteratees__=ze(this.__iteratees__),l.__takeCount__=this.__takeCount__,l.__views__=ze(this.__views__),l}function Jr(){if(this.__filtered__){var l=new At(this);l.__dir__=-1,l.__filtered__=!0}else l=this.clone(),l.__dir__*=-1;return l}function Oi(){var l=this.__wrapped__.value(),i=this.__dir__,r=vt(l),s=i<0,d=r?l.length:0,b=Dh(0,d,this.__views__),_=b.start,k=b.end,O=k-_,V=s?k:_-1,J=this.__iteratees__,W=J.length,tt=0,at=he(O,this.__takeCount__);if(!r||!s&&d==O&&at==O)return ph(l,this.__actions__);var mt=[];t:for(;O--&&tt<at;){V+=i;for(var wt=-1,pt=l[V];++wt<W;){var Rt=J[wt],Gt=Rt.iteratee,nl=Rt.type,Cn=Gt(pt);if(nl==ul)pt=Cn;else if(!Cn){if(nl==en)continue t;break t}}mt[tt++]=pt}return mt}At.prototype=Tn(Gn.prototype),At.prototype.constructor=At;function ve(l){var i=-1,r=l==null?0:l.length;for(this.clear();++i<r;){var s=l[i];this.set(s[0],s[1])}}function Wr(){this.__data__=fn?fn(null):{},this.size=0}function Is(l){var i=this.has(l)&&delete this.__data__[l];return this.size-=i?1:0,i}function pg(l){var i=this.__data__;if(fn){var r=i[l];return r===x?c:r}return Yt.call(i,l)?i[l]:c}function vg(l){var i=this.__data__;return fn?i[l]!==c:Yt.call(i,l)}function bg(l,i){var r=this.__data__;return this.size+=this.has(l)?0:1,r[l]=fn&&i===c?x:i,this}ve.prototype.clear=Wr,ve.prototype.delete=Is,ve.prototype.get=pg,ve.prototype.has=vg,ve.prototype.set=bg;function ne(l){var i=-1,r=l==null?0:l.length;for(this.clear();++i<r;){var s=l[i];this.set(s[0],s[1])}}function Kc(){this.__data__=[],this.size=0}function Aa(l){var i=this.__data__,r=ku(i,l);if(r<0)return!1;var s=i.length-1;return r==s?i.pop():Vr.call(i,r,1),--this.size,!0}function Ta(l){var i=this.__data__,r=ku(i,l);return r<0?c:i[r][1]}function jc(l){return ku(this.__data__,l)>-1}function Tu(l,i){var r=this.__data__,s=ku(r,l);return s<0?(++this.size,r.push([l,i])):r[s][1]=i,this}ne.prototype.clear=Kc,ne.prototype.delete=Aa,ne.prototype.get=Ta,ne.prototype.has=jc,ne.prototype.set=Tu;function on(l){var i=-1,r=l==null?0:l.length;for(this.clear();++i<r;){var s=l[i];this.set(s[0],s[1])}}function yg(){this.size=0,this.__data__={hash:new ve,map:new(ke||ne),string:new ve}}function Ps(l){var i=Ou(this,l).delete(l);return this.size-=i?1:0,i}function Sg(l){return Ou(this,l).get(l)}function th(l){return Ou(this,l).has(l)}function Ri(l,i){var r=Ou(this,l),s=r.size;return r.set(l,i),this.size+=r.size==s?0:1,this}on.prototype.clear=yg,on.prototype.delete=Ps,on.prototype.get=Sg,on.prototype.has=th,on.prototype.set=Ri;function wl(l){var i=-1,r=l==null?0:l.length;for(this.__data__=new on;++i<r;)this.add(l[i])}function Fr(l){return this.__data__.set(l,x),this}function eh(l){return this.__data__.has(l)}wl.prototype.add=wl.prototype.push=Fr,wl.prototype.has=eh;function $e(l){var i=this.__data__=new ne(l);this.size=i.size}function nh(){this.__data__=new ne,this.size=0}function $r(l){var i=this.__data__,r=i.delete(l);return this.size=i.size,r}function Jc(l){return this.__data__.get(l)}function lh(l){return this.__data__.has(l)}function Ir(l,i){var r=this.__data__;if(r instanceof ne){var s=r.__data__;if(!ke||s.length<S-1)return s.push([l,i]),this.size=++r.size,this;r=this.__data__=new on(s)}return r.set(l,i),this.size=r.size,this}$e.prototype.clear=nh,$e.prototype.delete=$r,$e.prototype.get=Jc,$e.prototype.has=lh,$e.prototype.set=Ir;function Wc(l,i){var r=vt(l),s=!r&&Hl(l),d=!r&&!s&&gi(l),b=!r&&!s&&!d&&Ku(l),_=r||s||d||b,k=_?Kl(l.length,Js):[],O=k.length;for(var V in l)(i||Yt.call(l,V))&&!(_&&(V=="length"||d&&(V=="offset"||V=="parent")||b&&(V=="buffer"||V=="byteLength"||V=="byteOffset")||Pn(V,O)))&&k.push(V);return k}function wu(l){var i=l.length;return i?l[cf(0,i-1)]:c}function ih(l,i){return $a(ze(l),$n(i,0,l.length))}function uh(l){return $a(ze(l))}function ol(l,i,r){(r!==c&&!vn(l[i],r)||r===c&&!(i in l))&&Fn(l,i,r)}function Ui(l,i,r){var s=l[i];(!(Yt.call(l,i)&&vn(s,r))||r===c&&!(i in l))&&Fn(l,i,r)}function ku(l,i){for(var r=l.length;r--;)if(vn(l[r][0],i))return r;return-1}function Pl(l,i,r,s){return zl(l,function(d,b,_){i(s,d,r(d),_)}),s}function kl(l,i){return l&&dn(i,Ce(i),l)}function wa(l,i){return l&&dn(i,Ge(i),l)}function Fn(l,i,r){i=="__proto__"&&va?va(l,i,{configurable:!0,enumerable:!0,value:r,writable:!0}):l[i]=r}function ka(l,i){for(var r=-1,s=i.length,d=H(s),b=l==null;++r<s;)d[r]=b?c:sr(l,i[r]);return d}function $n(l,i,r){return l===l&&(r!==c&&(l=l<=r?l:r),i!==c&&(l=l>=i?l:i)),l}function Ze(l,i,r,s,d,b){var _,k=i&M,O=i&N,V=i&F;if(r&&(_=d?r(l,s,d,b):r(l)),_!==c)return _;if(!me(l))return l;var J=vt(l);if(J){if(_=xo(l),!k)return ze(l,_)}else{var W=qe(l),tt=W==Fi||W==yr;if(gi(l))return go(l,k);if(W==Be||W==Ue||tt&&!d){if(_=O||tt?{}:Ao(l),!k)return O?qa(l,wa(_,l)):mf(l,kl(_,l))}else{if(!Kt[W])return d?l:{};_=To(l,W,k)}}b||(b=new $e);var at=b.get(l);if(at)return at;b.set(l,_),xd(l)?l.forEach(function(pt){_.add(Ze(pt,i,r,pt,l,b))}):Gf(l)&&l.forEach(function(pt,Rt){_.set(Rt,Ze(pt,i,r,Rt,l,b))});var mt=V?O?yf:bf:O?Ge:Ce,wt=J?c:mt(l);return Un(wt||l,function(pt,Rt){wt&&(Rt=pt,pt=l[Rt]),Ui(_,Rt,Ze(pt,i,r,Rt,l,b))}),_}function ah(l){var i=Ce(l);return function(r){return Fc(r,l,i)}}function Fc(l,i,r){var s=r.length;if(l==null)return!s;for(l=Nt(l);s--;){var d=r[s],b=i[d],_=l[d];if(_===c&&!(d in l)||!b(_))return!1}return!0}function Bi(l,i,r){if(typeof l!="function")throw new An(y);return Uu(function(){l.apply(c,r)},i)}function ti(l,i,r,s){var d=-1,b=Ur,_=!0,k=l.length,O=[],V=i.length;if(!k)return O;r&&(i=se(i,_n(r))),s?(b=Rc,_=!1):i.length>=S&&(b=mu,_=!1,i=new wl(i));t:for(;++d<k;){var J=l[d],W=r==null?J:r(J);if(J=s||J!==0?J:0,_&&W===W){for(var tt=V;tt--;)if(i[tt]===W)continue t;O.push(J)}else b(i,W,s)||O.push(J)}return O}var zl=bo(Ve),Pr=bo(Eu,!0);function $c(l,i){var r=!0;return zl(l,function(s,d,b){return r=!!i(s,d,b),r}),r}function In(l,i,r){for(var s=-1,d=l.length;++s<d;){var b=l[s],_=i(b);if(_!=null&&(k===c?_===_&&!tn(_):r(_,k)))var k=_,O=b}return O}function xt(l,i,r,s){var d=l.length;for(r=yt(r),r<0&&(r=-r>d?0:d+r),s=s===c||s>d?d:yt(s),s<0&&(s+=d),s=r>s?0:yl(s);r<s;)l[r++]=i;return l}function jt(l,i){var r=[];return zl(l,function(s,d,b){i(s,d,b)&&r.push(s)}),r}function Dt(l,i,r,s,d){var b=-1,_=l.length;for(r||(r=Cl),d||(d=[]);++b<_;){var k=l[b];i>0&&r(k)?i>1?Dt(k,i-1,r,s,d):xl(d,k):s||(d[d.length]=k)}return d}var zu=Xi(),Li=Xi(!0);function Ve(l,i){return l&&zu(l,i,Ce)}function Eu(l,i){return l&&Li(l,i,Ce)}function ei(l,i){return fl(i,function(r){return tl(l[r])})}function Yn(l,i){i=Ml(i,l);for(var r=0,s=i.length;l!=null&&r<s;)l=l[Vt(i[r++])];return r&&r==s?l:c}function rh(l,i,r){var s=i(l);return vt(l)?s:xl(s,r(l))}function Ot(l){return l==null?l===c?Wd:Jd:Fe&&Fe in Nt(l)?Mh(l):Bh(l)}function za(l,i){return l>i}function Ic(l,i){return l!=null&&Yt.call(l,i)}function fh(l,i){return l!=null&&i in Nt(l)}function ch(l,i,r){return l>=he(i,r)&&l<ce(i,r)}function Pc(l,i,r){for(var s=r?Rc:Ur,d=l[0].length,b=l.length,_=b,k=H(b),O=1/0,V=[];_--;){var J=l[_];_&&i&&(J=se(J,_n(i))),O=he(J.length,O),k[_]=!r&&(i||d>=120&&J.length>=120)?new wl(_&&J):c}J=l[0];var W=-1,tt=k[0];t:for(;++W<d&&V.length<O;){var at=J[W],mt=i?i(at):at;if(at=r||at!==0?at:0,!(tt?mu(tt,mt):s(V,mt,r))){for(_=b;--_;){var wt=k[_];if(!(wt?mu(wt,mt):s(l[_],mt,r)))continue t}tt&&tt.push(mt),V.push(at)}}return V}function to(l,i,r,s){return Ve(l,function(d,b,_){i(s,r(d),b,_)}),s}function Hi(l,i,r){i=Ml(i,l),l=Ru(l,i);var s=l==null?l:l[Vt(Ke(i))];return s==null?c:an(s,l,r)}function tf(l){return pe(l)&&Ot(l)==Ue}function sn(l){return pe(l)&&Ot(l)==Xe}function Ae(l){return pe(l)&&Ot(l)==al}function Ni(l,i,r,s,d){return l===i?!0:l==null||i==null||!pe(l)&&!pe(i)?l!==l&&i!==i:Ea(l,i,r,s,Ni,d)}function Ea(l,i,r,s,d,b){var _=vt(l),k=vt(i),O=_?we:qe(l),V=k?we:qe(i);O=O==Ue?Be:O,V=V==Ue?Be:V;var J=O==Be,W=V==Be,tt=O==V;if(tt&&gi(l)){if(!gi(i))return!1;_=!0,J=!1}if(tt&&!J)return b||(b=new $e),_||Ku(l)?zh(l,i,r,s,d,b):Eh(l,i,O,r,s,d,b);if(!(r&Z)){var at=J&&Yt.call(l,"__wrapped__"),mt=W&&Yt.call(i,"__wrapped__");if(at||mt){var wt=at?l.value():l,pt=mt?i.value():i;return b||(b=new $e),d(wt,pt,r,s,b)}}return tt?(b||(b=new $e),Ne(l,i,r,s,d,b)):!1}function ef(l){return pe(l)&&qe(l)==bn}function Ma(l,i,r,s){var d=r.length,b=d,_=!s;if(l==null)return!b;for(l=Nt(l);d--;){var k=r[d];if(_&&k[2]?k[1]!==l[k[0]]:!(k[0]in l))return!1}for(;++d<b;){k=r[d];var O=k[0],V=l[O],J=k[1];if(_&&k[2]){if(V===c&&!(O in l))return!1}else{var W=new $e;if(s)var tt=s(V,J,O,l,i,W);if(!(tt===c?Ni(J,V,Z|I,s,W):tt))return!1}}return!0}function sl(l){if(!me(l)||wf(l))return!1;var i=tl(l)?Xc:_s;return i.test(Rl(l))}function nf(l){return pe(l)&&Ot(l)==$i}function eo(l){return pe(l)&&qe(l)==nn}function no(l){return pe(l)&&ji(l.length)&&!!Pt[Ot(l)]}function lo(l){return typeof l=="function"?l:l==null?B:typeof l=="object"?vt(l)?af(l[0],l[1]):uo(l):qt(l)}function lf(l){if(!le(l))return Nn(l);var i=[];for(var r in Nt(l))Yt.call(l,r)&&r!="constructor"&&i.push(r);return i}function oh(l){if(!me(l))return ui(l);var i=le(l),r=[];for(var s in l)s=="constructor"&&(i||!Yt.call(l,s))||r.push(s);return r}function uf(l,i){return l<i}function io(l,i){var r=-1,s=Me(l)?H(l.length):[];return zl(l,function(d,b,_){s[++r]=i(d,b,_)}),s}function uo(l){var i=_f(l);return i.length==1&&i[0][2]?Rh(i[0][0],i[0][1]):function(r){return r===l||Ma(r,l,i)}}function af(l,i){return Af(l)&&Oh(i)?Rh(Vt(l),i):function(r){var s=sr(r,l);return s===c&&s===i?Fu(r,l):Ni(i,s,Z|I)}}function Da(l,i,r,s,d){l!==i&&zu(i,function(b,_){if(d||(d=new $e),me(b))_g(l,i,_,r,Da,s,d);else{var k=s?s(kf(l,_),b,_+"",l,i,d):c;k===c&&(k=b),ol(l,_,k)}},Ge)}function _g(l,i,r,s,d,b,_){var k=kf(l,r),O=kf(i,r),V=_.get(O);if(V){ol(l,r,V);return}var J=b?b(k,O,r+"",l,i,_):c,W=J===c;if(W){var tt=vt(O),at=!tt&&gi(O),mt=!tt&&!at&&Ku(O);J=O,tt||at||mt?vt(k)?J=k:Se(k)?J=ze(k):at?(W=!1,J=go(O,!0)):mt?(W=!1,J=po(O,!0)):J=[]:Vn(O)||Hl(O)?(J=k,Hl(k)?J=wd(k):(!me(k)||tl(k))&&(J=Ao(O))):W=!1}W&&(_.set(O,J),d(J,O,s,b,_),_.delete(O)),ol(l,r,J)}function ao(l,i){var r=l.length;if(r)return i+=i<0?r:0,Pn(i,r)?l[i]:c}function ro(l,i,r){i.length?i=se(i,function(b){return vt(b)?function(_){return Yn(_,b.length===1?b[0]:b)}:b}):i=[B];var s=-1;i=se(i,_n(ct()));var d=io(l,function(b,_,k){var O=se(i,function(V){return V(b)});return{criteria:O,index:++s,value:b}});return qs(d,function(b,_){return Sh(b,_,r)})}function sh(l,i){return rf(l,i,function(r,s){return Fu(l,s)})}function rf(l,i,r){for(var s=-1,d=i.length,b={};++s<d;){var _=i[s],k=Yn(l,_);r(k,_)&&Du(b,Ml(_,l),k)}return b}function hh(l){return function(i){return Yn(i,l)}}function ff(l,i,r,s){var d=s?Lc:du,b=-1,_=i.length,k=l;for(l===i&&(i=ze(i)),r&&(k=se(l,_n(r)));++b<_;)for(var O=0,V=i[b],J=r?r(V):V;(O=d(k,J,O,s))>-1;)k!==l&&Vr.call(k,O,1),Vr.call(l,O,1);return l}function fo(l,i){for(var r=l?i.length:0,s=r-1;r--;){var d=i[r];if(r==s||d!==b){var b=d;Pn(d)?Vr.call(l,d,1):Ua(l,d)}}return l}function cf(l,i){return l+Al(Zc()*(i-l+1))}function dh(l,i,r,s){for(var d=-1,b=ce(xu((i-l)/(r||1)),0),_=H(b);b--;)_[s?b:++d]=l,l+=r;return _}function co(l,i){var r="";if(!l||i<1||i>ft)return r;do i%2&&(r+=l),i=Al(i/2),i&&(l+=l);while(i);return r}function _t(l,i){return Wa(wo(l,i,B),l+"")}function Mu(l){return wu(Iu(l))}function of(l,i){var r=Iu(l);return $a(r,$n(i,0,r.length))}function Du(l,i,r,s){if(!me(l))return l;i=Ml(i,l);for(var d=-1,b=i.length,_=b-1,k=l;k!=null&&++d<b;){var O=Vt(i[d]),V=r;if(O==="__proto__"||O==="constructor"||O==="prototype")return l;if(d!=_){var J=k[O];V=s?s(J,O,k):c,V===c&&(V=me(J)?J:Pn(i[d+1])?[]:{})}Ui(k,O,V),k=k[O]}return l}var Ca=Au?function(l,i){return Au.set(l,i),l}:B,Oa=va?function(l,i){return va(l,"toString",{configurable:!0,enumerable:!1,value:U(i),writable:!0})}:B;function gh(l){return $a(Iu(l))}function wn(l,i,r){var s=-1,d=l.length;i<0&&(i=-i>d?0:d+i),r=r>d?d:r,r<0&&(r+=d),d=i>r?0:r-i>>>0,i>>>=0;for(var b=H(d);++s<d;)b[s]=l[s+i];return b}function mh(l,i){var r;return zl(l,function(s,d,b){return r=i(s,d,b),!r}),!!r}function Ra(l,i,r){var s=0,d=l==null?s:l.length;if(typeof i=="number"&&i===i&&d<=Zt){for(;s<d;){var b=s+d>>>1,_=l[b];_!==null&&!tn(_)&&(r?_<=i:_<i)?s=b+1:d=b}return d}return sf(l,i,B,r)}function sf(l,i,r,s){var d=0,b=l==null?0:l.length;if(b===0)return 0;i=r(i);for(var _=i!==i,k=i===null,O=tn(i),V=i===c;d<b;){var J=Al((d+b)/2),W=r(l[J]),tt=W!==c,at=W===null,mt=W===W,wt=tn(W);if(_)var pt=s||mt;else V?pt=mt&&(s||tt):k?pt=mt&&tt&&(s||!at):O?pt=mt&&tt&&!at&&(s||!wt):at||wt?pt=!1:pt=s?W<=i:W<i;pt?d=J+1:b=J}return he(b,fe)}function oo(l,i){for(var r=-1,s=l.length,d=0,b=[];++r<s;){var _=l[r],k=i?i(_):_;if(!r||!vn(k,O)){var O=k;b[d++]=_===0?0:_}}return b}function hf(l){return typeof l=="number"?l:tn(l)?bt:+l}function hn(l){if(typeof l=="string")return l;if(vt(l))return se(l,hn)+"";if(tn(l))return Il?Il.call(l):"";var i=l+"";return i=="0"&&1/l==-$?"-0":i}function El(l,i,r){var s=-1,d=Ur,b=l.length,_=!0,k=[],O=k;if(r)_=!1,d=Rc;else if(b>=S){var V=i?null:wh(l);if(V)return Xr(V);_=!1,d=mu,O=new wl}else O=i?[]:k;t:for(;++s<b;){var J=l[s],W=i?i(J):J;if(J=r||J!==0?J:0,_&&W===W){for(var tt=O.length;tt--;)if(O[tt]===W)continue t;i&&O.push(W),k.push(J)}else d(O,W,r)||(O!==k&&O.push(W),k.push(J))}return k}function Ua(l,i){return i=Ml(i,l),l=Ru(l,i),l==null||delete l[Vt(Ke(i))]}function so(l,i,r,s){return Du(l,i,r(Yn(l,i)),s)}function Ba(l,i,r,s){for(var d=l.length,b=s?d:-1;(s?b--:++b<d)&&i(l[b],b,l););return r?wn(l,s?0:b,s?b+1:d):wn(l,s?b+1:0,s?d:b)}function ph(l,i){var r=l;return r instanceof At&&(r=r.value()),Uc(i,function(s,d){return d.func.apply(d.thisArg,xl([s],d.args))},r)}function La(l,i,r){var s=l.length;if(s<2)return s?El(l[0]):[];for(var d=-1,b=H(s);++d<s;)for(var _=l[d],k=-1;++k<s;)k!=d&&(b[d]=ti(b[d]||_,l[k],i,r));return El(Dt(b,1),i,r)}function ho(l,i,r){for(var s=-1,d=l.length,b=i.length,_={};++s<d;){var k=s<b?i[s]:c;r(_,l[s],k)}return _}function df(l){return Se(l)?l:[]}function Ha(l){return typeof l=="function"?l:B}function Ml(l,i){return vt(l)?l:Af(l,i)?[l]:ml(Xt(l))}var vh=_t;function ni(l,i,r){var s=l.length;return r=r===c?s:r,!i&&r>=s?l:wn(l,i,r)}var bh=Kr||function(l){return _e.clearTimeout(l)};function go(l,i){if(i)return l.slice();var r=l.length,s=ki?ki(r):new l.constructor(r);return l.copy(s),s}function qi(l){var i=new l.constructor(l.byteLength);return new _u(i).set(new _u(l)),i}function mo(l,i){var r=i?qi(l.buffer):l.buffer;return new l.constructor(r,l.byteOffset,l.byteLength)}function gf(l){var i=new l.constructor(l.source,iu.exec(l));return i.lastIndex=l.lastIndex,i}function yh(l){return Ci?Nt(Ci.call(l)):{}}function po(l,i){var r=i?qi(l.buffer):l.buffer;return new l.constructor(r,l.byteOffset,l.length)}function Na(l,i){if(l!==i){var r=l!==c,s=l===null,d=l===l,b=tn(l),_=i!==c,k=i===null,O=i===i,V=tn(i);if(!k&&!V&&!b&&l>i||b&&_&&O&&!k&&!V||s&&_&&O||!r&&O||!d)return 1;if(!s&&!b&&!V&&l<i||V&&r&&d&&!s&&!b||k&&r&&d||!_&&d||!O)return-1}return 0}function Sh(l,i,r){for(var s=-1,d=l.criteria,b=i.criteria,_=d.length,k=r.length;++s<_;){var O=Na(d[s],b[s]);if(O){if(s>=k)return O;var V=r[s];return O*(V=="desc"?-1:1)}}return l.index-i.index}function vo(l,i,r,s){for(var d=-1,b=l.length,_=r.length,k=-1,O=i.length,V=ce(b-_,0),J=H(O+V),W=!s;++k<O;)J[k]=i[k];for(;++d<_;)(W||d<b)&&(J[r[d]]=l[d]);for(;V--;)J[k++]=l[d++];return J}function _h(l,i,r,s){for(var d=-1,b=l.length,_=-1,k=r.length,O=-1,V=i.length,J=ce(b-k,0),W=H(J+V),tt=!s;++d<J;)W[d]=l[d];for(var at=d;++O<V;)W[at+O]=i[O];for(;++_<k;)(tt||d<b)&&(W[at+r[_]]=l[d++]);return W}function ze(l,i){var r=-1,s=l.length;for(i||(i=H(s));++r<s;)i[r]=l[r];return i}function dn(l,i,r,s){var d=!r;r||(r={});for(var b=-1,_=i.length;++b<_;){var k=i[b],O=s?s(r[k],l[k],k,r,l):c;O===c&&(O=l[k]),d?Fn(r,k,O):Ui(r,k,O)}return r}function mf(l,i){return dn(l,Va(l),i)}function qa(l,i){return dn(l,xf(l),i)}function Gi(l,i){return function(r,s){var d=vt(r)?ig:Pl,b=i?i():{};return d(r,l,ct(s,2),b)}}function Yi(l){return _t(function(i,r){var s=-1,d=r.length,b=d>1?r[d-1]:c,_=d>2?r[2]:c;for(b=l.length>3&&typeof b=="function"?(d--,b):c,_&&Pe(r[0],r[1],_)&&(b=d<3?c:b,d=1),i=Nt(i);++s<d;){var k=r[s];k&&l(i,k,s,b)}return i})}function bo(l,i){return function(r,s){if(r==null)return r;if(!Me(r))return l(r,s);for(var d=r.length,b=i?d:-1,_=Nt(r);(i?b--:++b<d)&&s(_[b],b,_)!==!1;);return r}}function Xi(l){return function(i,r,s){for(var d=-1,b=Nt(i),_=s(i),k=_.length;k--;){var O=_[l?k:++d];if(r(b[O],O,b)===!1)break}return i}}function xh(l,i,r){var s=i&et,d=Xn(l);function b(){var _=this&&this!==_e&&this instanceof b?d:l;return _.apply(s?r:this,arguments)}return b}function kn(l){return function(i){i=Xt(i);var r=jl(i)?Bn(i):c,s=r?r[0]:i.charAt(0),d=r?ni(r,1).join(""):i.slice(1);return s[l]()+d}}function Ie(l){return function(i){return Uc(a(Nl(i).replace(ca,"")),l,"")}}function Xn(l){return function(){var i=arguments;switch(i.length){case 0:return new l;case 1:return new l(i[0]);case 2:return new l(i[0],i[1]);case 3:return new l(i[0],i[1],i[2]);case 4:return new l(i[0],i[1],i[2],i[3]);case 5:return new l(i[0],i[1],i[2],i[3],i[4]);case 6:return new l(i[0],i[1],i[2],i[3],i[4],i[5]);case 7:return new l(i[0],i[1],i[2],i[3],i[4],i[5],i[6])}var r=Tn(l.prototype),s=l.apply(r,i);return me(s)?s:r}}function Ah(l,i,r){var s=Xn(l);function d(){for(var b=arguments.length,_=H(b),k=b,O=Zi(d);k--;)_[k]=arguments[k];var V=b<3&&_[0]!==O&&_[b-1]!==O?[]:Jl(_,O);if(b-=V.length,b<r)return yo(l,i,be,d.placeholder,c,_,V,c,c,r-b);var J=this&&this!==_e&&this instanceof d?s:l;return an(J,this,_)}return d}function Dl(l){return function(i,r,s){var d=Nt(i);if(!Me(i)){var b=ct(r,3);i=Ce(i),r=function(k){return b(d[k],k,d)}}var _=l(i,r,s);return _>-1?d[b?i[_]:_]:c}}function hl(l){return oe(function(i){var r=i.length,s=r,d=cn.prototype.thru;for(l&&i.reverse();s--;){var b=i[s];if(typeof b!="function")throw new An(y);if(d&&!_&&Za(b)=="wrapper")var _=new cn([],!0)}for(s=_?s:r;++s<r;){b=i[s];var k=Za(b),O=k=="wrapper"?Sf(b):c;O&&Tf(O[0])&&O[1]==($t|it|Ct|St)&&!O[4].length&&O[9]==1?_=_[Za(O[0])].apply(_,O[3]):_=b.length==1&&Tf(b)?_[k]():_.thru(b)}return function(){var V=arguments,J=V[0];if(_&&V.length==1&&vt(J))return _.plant(J).value();for(var W=0,tt=r?i[W].apply(this,V):J;++W<r;)tt=i[W].call(this,tt);return tt}})}function be(l,i,r,s,d,b,_,k,O,V){var J=i&$t,W=i&et,tt=i&ut,at=i&(it|Ut),mt=i&ue,wt=tt?c:Xn(l);function pt(){for(var Rt=arguments.length,Gt=H(Rt),nl=Rt;nl--;)Gt[nl]=arguments[nl];if(at)var Cn=Zi(pt),ll=sg(Gt,Cn);if(s&&(Gt=vo(Gt,s,d,at)),b&&(Gt=_h(Gt,b,_,at)),Rt-=ll,at&&Rt<V){var Oe=Jl(Gt,Cn);return yo(l,i,be,pt.placeholder,r,Gt,Oe,k,O,V-Rt)}var ql=W?r:this,Ji=tt?ql[l]:l;return Rt=Gt.length,k?Gt=gl(Gt,k):mt&&Rt>1&&Gt.reverse(),J&&O<Rt&&(Gt.length=O),this&&this!==_e&&this instanceof pt&&(Ji=wt||Xn(Ji)),Ji.apply(ql,Gt)}return pt}function Ga(l,i){return function(r,s){return to(r,l,i(s),{})}}function Cu(l,i){return function(r,s){var d;if(r===c&&s===c)return i;if(r!==c&&(d=r),s!==c){if(d===c)return s;typeof r=="string"||typeof s=="string"?(r=hn(r),s=hn(s)):(r=hf(r),s=hf(s)),d=l(r,s)}return d}}function Ya(l){return oe(function(i){return i=se(i,_n(ct())),_t(function(r){var s=this;return l(i,function(d){return an(d,s,r)})})})}function Xa(l,i){i=i===c?" ":hn(i);var r=i.length;if(r<2)return r?co(i,l):i;var s=co(i,xu(l/cl(i)));return jl(i)?ni(Bn(s),0,l).join(""):s.slice(0,l)}function Th(l,i,r,s){var d=i&et,b=Xn(l);function _(){for(var k=-1,O=arguments.length,V=-1,J=s.length,W=H(J+O),tt=this&&this!==_e&&this instanceof _?b:l;++V<J;)W[V]=s[V];for(;O--;)W[V++]=arguments[++k];return an(tt,d?r:this,W)}return _}function li(l){return function(i,r,s){return s&&typeof s!="number"&&Pe(i,r,s)&&(r=s=c),i=bl(i),r===c?(r=i,i=0):r=bl(r),s=s===c?i<r?1:-1:bl(s),dh(i,r,s,l)}}function Qi(l){return function(i,r){return typeof i=="string"&&typeof r=="string"||(i=Kn(i),r=Kn(r)),l(i,r)}}function yo(l,i,r,s,d,b,_,k,O,V){var J=i&it,W=J?_:c,tt=J?c:_,at=J?b:c,mt=J?c:b;i|=J?Ct:Lt,i&=~(J?Lt:Ct),i&dt||(i&=-4);var wt=[l,i,d,at,W,mt,tt,k,O,V],pt=r.apply(c,wt);return Tf(l)&&zf(pt,wt),pt.placeholder=s,Ef(pt,l,i)}function pf(l){var i=xe[l];return function(r,s){if(r=Kn(r),s=s==null?0:he(yt(s),292),s&&ba(r)){var d=(Xt(r)+"e").split("e"),b=i(d[0]+"e"+(+d[1]+s));return d=(Xt(b)+"e").split("e"),+(d[0]+"e"+(+d[1]-s))}return i(r)}}var wh=Mt&&1/Xr(new Mt([,-0]))[1]==$?function(l){return new Mt(l)}:lt;function Qa(l){return function(i){var r=qe(i);return r==bn?Yr(i):r==nn?hg(i):og(i,l(i))}}function dl(l,i,r,s,d,b,_,k){var O=i&ut;if(!O&&typeof l!="function")throw new An(y);var V=s?s.length:0;if(V||(i&=-97,s=d=c),_=_===c?_:ce(yt(_),0),k=k===c?k:yt(k),V-=d?d.length:0,i&Lt){var J=s,W=d;s=d=c}var tt=O?c:Sf(l),at=[l,i,r,s,d,J,W,b,_,k];if(tt&&Ja(at,tt),l=at[0],i=at[1],r=at[2],s=at[3],d=at[4],k=at[9]=at[9]===c?O?0:l.length:ce(at[9]-V,0),!k&&i&(it|Ut)&&(i&=-25),!i||i==et)var mt=xh(l,i,r);else i==it||i==Ut?mt=Ah(l,i,k):(i==Ct||i==(et|Ct))&&!d.length?mt=Th(l,i,r,s):mt=be.apply(c,at);var wt=tt?Ca:zf;return Ef(wt(mt,at),l,i)}function vf(l,i,r,s){return l===c||vn(l,yu[r])&&!Yt.call(s,r)?i:l}function So(l,i,r,s,d,b){return me(l)&&me(i)&&(b.set(i,l),Da(l,i,c,So,b),b.delete(i)),l}function kh(l){return Vn(l)?c:l}function zh(l,i,r,s,d,b){var _=r&Z,k=l.length,O=i.length;if(k!=O&&!(_&&O>k))return!1;var V=b.get(l),J=b.get(i);if(V&&J)return V==i&&J==l;var W=-1,tt=!0,at=r&I?new wl:c;for(b.set(l,i),b.set(i,l);++W<k;){var mt=l[W],wt=i[W];if(s)var pt=_?s(wt,mt,W,i,l,b):s(mt,wt,W,l,i,b);if(pt!==c){if(pt)continue;tt=!1;break}if(at){if(!Bc(i,function(Rt,Gt){if(!mu(at,Gt)&&(mt===Rt||d(mt,Rt,r,s,b)))return at.push(Gt)})){tt=!1;break}}else if(!(mt===wt||d(mt,wt,r,s,b))){tt=!1;break}}return b.delete(l),b.delete(i),tt}function Eh(l,i,r,s,d,b,_){switch(r){case yn:if(l.byteLength!=i.byteLength||l.byteOffset!=i.byteOffset)return!1;l=l.buffer,i=i.buffer;case Xe:return!(l.byteLength!=i.byteLength||!b(new _u(l),new _u(i)));case Gl:case al:case ta:return vn(+l,+i);case Si:return l.name==i.name&&l.message==i.message;case $i:case ea:return l==i+"";case bn:var k=Yr;case nn:var O=s&Z;if(k||(k=Xr),l.size!=i.size&&!O)return!1;var V=_.get(l);if(V)return V==i;s|=I,_.set(l,i);var J=zh(k(l),k(i),s,d,b,_);return _.delete(l),J;case na:if(Ci)return Ci.call(l)==Ci.call(i)}return!1}function Ne(l,i,r,s,d,b){var _=r&Z,k=bf(l),O=k.length,V=bf(i),J=V.length;if(O!=J&&!_)return!1;for(var W=O;W--;){var tt=k[W];if(!(_?tt in i:Yt.call(i,tt)))return!1}var at=b.get(l),mt=b.get(i);if(at&&mt)return at==i&&mt==l;var wt=!0;b.set(l,i),b.set(i,l);for(var pt=_;++W<O;){tt=k[W];var Rt=l[tt],Gt=i[tt];if(s)var nl=_?s(Gt,Rt,tt,i,l,b):s(Rt,Gt,tt,l,i,b);if(!(nl===c?Rt===Gt||d(Rt,Gt,r,s,b):nl)){wt=!1;break}pt||(pt=tt=="constructor")}if(wt&&!pt){var Cn=l.constructor,ll=i.constructor;Cn!=ll&&"constructor"in l&&"constructor"in i&&!(typeof Cn=="function"&&Cn instanceof Cn&&typeof ll=="function"&&ll instanceof ll)&&(wt=!1)}return b.delete(l),b.delete(i),wt}function oe(l){return Wa(wo(l,c,mn),l+"")}function bf(l){return rh(l,Ce,Va)}function yf(l){return rh(l,Ge,xf)}var Sf=Au?function(l){return Au.get(l)}:lt;function Za(l){for(var i=l.name+"",r=qn[i],s=Yt.call(qn,i)?r.length:0;s--;){var d=r[s],b=d.func;if(b==null||b==l)return d.name}return i}function Zi(l){var i=Yt.call(p,"placeholder")?p:l;return i.placeholder}function ct(){var l=p.iteratee||st;return l=l===st?lo:l,arguments.length?l(arguments[0],arguments[1]):l}function Ou(l,i){var r=l.__data__;return Ol(i)?r[typeof i=="string"?"string":"hash"]:r.map}function _f(l){for(var i=Ce(l),r=i.length;r--;){var s=i[r],d=l[s];i[r]=[s,d,Oh(d)]}return i}function ii(l,i){var r=Nc(l,i);return sl(r)?r:c}function Mh(l){var i=Yt.call(l,Fe),r=l[Fe];try{l[Fe]=c;var s=!0}catch{}var d=We.call(l);return s&&(i?l[Fe]=r:delete l[Fe]),d}var Va=Fl?function(l){return l==null?[]:(l=Nt(l),fl(Fl(l),function(i){return zi.call(l,i)}))}:i0,xf=Fl?function(l){for(var i=[];l;)xl(i,Va(l)),l=ma(l);return i}:i0,qe=Ot;(ya&&qe(new ya(new ArrayBuffer(1)))!=yn||ke&&qe(new ke)!=bn||de&&qe(de.resolve())!=ds||Mt&&qe(new Mt)!=nn||Wn&&qe(new Wn)!=la)&&(qe=function(l){var i=Ot(l),r=i==Be?l.constructor:c,s=r?Rl(r):"";if(s)switch(s){case Ws:return yn;case Fs:return bn;case Sa:return ds;case _a:return nn;case $s:return la}return i});function Dh(l,i,r){for(var s=-1,d=r.length;++s<d;){var b=r[s],_=b.size;switch(b.type){case"drop":l+=_;break;case"dropRight":i-=_;break;case"take":i=he(i,l+_);break;case"takeRight":l=ce(l,i-_);break}}return{start:l,end:i}}function _o(l){var i=l.match(tg);return i?i[1].split(Ss):[]}function Ka(l,i,r){i=Ml(i,l);for(var s=-1,d=i.length,b=!1;++s<d;){var _=Vt(i[s]);if(!(b=l!=null&&r(l,_)))break;l=l[_]}return b||++s!=d?b:(d=l==null?0:l.length,!!d&&ji(d)&&Pn(_,d)&&(vt(l)||Hl(l)))}function xo(l){var i=l.length,r=new l.constructor(i);return i&&typeof l[0]=="string"&&Yt.call(l,"index")&&(r.index=l.index,r.input=l.input),r}function Ao(l){return typeof l.constructor=="function"&&!le(l)?Tn(ma(l)):{}}function To(l,i,r){var s=l.constructor;switch(i){case Xe:return qi(l);case Gl:case al:return new s(+l);case yn:return mo(l,r);case Je:case cc:case oc:case sc:case Ii:case Pi:case Sl:case tu:case _i:return po(l,r);case bn:return new s;case ta:case ea:return new s(l);case $i:return gf(l);case nn:return new s;case na:return yh(l)}}function Ch(l,i){var r=i.length;if(!r)return l;var s=r-1;return i[s]=(r>1?"& ":"")+i[s],i=i.join(r>2?", ":" "),l.replace(Pd,`{
/* [wrapped with `+i+`] */
`)}function Cl(l){return vt(l)||Hl(l)||!!(rn&&l&&l[rn])}function Pn(l,i){var r=typeof l;return i=i??ft,!!i&&(r=="number"||r!="symbol"&&xi.test(l))&&l>-1&&l%1==0&&l<i}function Pe(l,i,r){if(!me(r))return!1;var s=typeof i;return(s=="number"?Me(r)&&Pn(i,r.length):s=="string"&&i in r)?vn(r[i],l):!1}function Af(l,i){if(vt(l))return!1;var r=typeof l;return r=="number"||r=="symbol"||r=="boolean"||l==null||tn(l)?!0:Id.test(l)||!ys.test(l)||i!=null&&l in Nt(i)}function Ol(l){var i=typeof l;return i=="string"||i=="number"||i=="symbol"||i=="boolean"?l!=="__proto__":l===null}function Tf(l){var i=Za(l),r=p[i];if(typeof r!="function"||!(i in At.prototype))return!1;if(l===r)return!0;var s=Sf(r);return!!s&&l===s[0]}function wf(l){return!!Yc&&Yc in l}var ja=da?tl:u0;function le(l){var i=l&&l.constructor,r=typeof i=="function"&&i.prototype||yu;return l===r}function Oh(l){return l===l&&!me(l)}function Rh(l,i){return function(r){return r==null?!1:r[l]===i&&(i!==c||l in Nt(r))}}function Uh(l){var i=ur(l,function(s){return r.size===w&&r.clear(),s}),r=i.cache;return i}function Ja(l,i){var r=l[1],s=i[1],d=r|s,b=d<(et|ut|$t),_=s==$t&&r==it||s==$t&&r==St&&l[7].length<=i[8]||s==($t|St)&&i[7].length<=i[8]&&r==it;if(!(b||_))return l;s&et&&(l[2]=i[2],d|=r&et?0:dt);var k=i[3];if(k){var O=l[3];l[3]=O?vo(O,k,i[4]):k,l[4]=O?Jl(l[3],q):i[4]}return k=i[5],k&&(O=l[5],l[5]=O?_h(O,k,i[6]):k,l[6]=O?Jl(l[5],q):i[6]),k=i[7],k&&(l[7]=k),s&$t&&(l[8]=l[8]==null?i[8]:he(l[8],i[8])),l[9]==null&&(l[9]=i[9]),l[0]=i[0],l[1]=d,l}function ui(l){var i=[];if(l!=null)for(var r in Nt(l))i.push(r);return i}function Bh(l){return We.call(l)}function wo(l,i,r){return i=ce(i===c?l.length-1:i,0),function(){for(var s=arguments,d=-1,b=ce(s.length-i,0),_=H(b);++d<b;)_[d]=s[i+d];d=-1;for(var k=H(i+1);++d<i;)k[d]=s[d];return k[i]=r(_),an(l,this,k)}}function Ru(l,i){return i.length<2?l:Yn(l,wn(i,0,-1))}function gl(l,i){for(var r=l.length,s=he(i.length,r),d=ze(l);s--;){var b=i[s];l[s]=Pn(b,r)?d[b]:c}return l}function kf(l,i){if(!(i==="constructor"&&typeof l[i]=="function")&&i!="__proto__")return l[i]}var zf=Fa(Ca),Uu=Qc||function(l,i){return _e.setTimeout(l,i)},Wa=Fa(Oa);function Ef(l,i,r){var s=i+"";return Wa(l,Ch(s,Lh(_o(s),r)))}function Fa(l){var i=0,r=0;return function(){var s=Tl(),d=Qt-(s-r);if(r=s,d>0){if(++i>=Et)return arguments[0]}else i=0;return l.apply(c,arguments)}}function $a(l,i){var r=-1,s=l.length,d=s-1;for(i=i===c?s:i;++r<i;){var b=cf(r,d),_=l[b];l[b]=l[r],l[r]=_}return l.length=i,l}var ml=Uh(function(l){var i=[];return l.charCodeAt(0)===46&&i.push(""),l.replace(Yl,function(r,s,d,b){i.push(d?b.replace(nu,"$1"):s||r)}),i});function Vt(l){if(typeof l=="string"||tn(l))return l;var i=l+"";return i=="0"&&1/l==-$?"-0":i}function Rl(l){if(l!=null){try{return ga.call(l)}catch{}try{return l+""}catch{}}return""}function Lh(l,i){return Un(Te,function(r){var s="_."+r[0];i&r[1]&&!Ur(l,s)&&l.push(s)}),l.sort()}function Ee(l){if(l instanceof At)return l.clone();var i=new cn(l.__wrapped__,l.__chain__);return i.__actions__=ze(l.__actions__),i.__index__=l.__index__,i.__values__=l.__values__,i}function xg(l,i,r){(r?Pe(l,i,r):i===c)?i=1:i=ce(yt(i),0);var s=l==null?0:l.length;if(!s||i<1)return[];for(var d=0,b=0,_=H(xu(s/i));d<s;)_[b++]=wn(l,d,d+=i);return _}function Hh(l){for(var i=-1,r=l==null?0:l.length,s=0,d=[];++i<r;){var b=l[i];b&&(d[s++]=b)}return d}function Nh(){var l=arguments.length;if(!l)return[];for(var i=H(l-1),r=arguments[0],s=l;s--;)i[s-1]=arguments[s];return xl(vt(r)?ze(r):[r],Dt(i,1))}var ge=_t(function(l,i){return Se(l)?ti(l,Dt(i,1,Se,!0)):[]}),gn=_t(function(l,i){var r=Ke(i);return Se(r)&&(r=c),Se(l)?ti(l,Dt(i,1,Se,!0),ct(r,2)):[]}),Ul=_t(function(l,i){var r=Ke(i);return Se(r)&&(r=c),Se(l)?ti(l,Dt(i,1,Se,!0),c,r):[]});function qh(l,i,r){var s=l==null?0:l.length;return s?(i=r||i===c?1:yt(i),wn(l,i<0?0:i,s)):[]}function Gh(l,i,r){var s=l==null?0:l.length;return s?(i=r||i===c?1:yt(i),i=s-i,wn(l,0,i<0?0:i)):[]}function Ag(l,i){return l&&l.length?Ba(l,ct(i,3),!0,!0):[]}function ko(l,i){return l&&l.length?Ba(l,ct(i,3),!0):[]}function zn(l,i,r,s){var d=l==null?0:l.length;return d?(r&&typeof r!="number"&&Pe(l,i,r)&&(r=0,s=d),xt(l,i,r,s)):[]}function Qn(l,i,r){var s=l==null?0:l.length;if(!s)return-1;var d=r==null?0:yt(r);return d<0&&(d=ce(s+d,0)),Br(l,ct(i,3),d)}function zo(l,i,r){var s=l==null?0:l.length;if(!s)return-1;var d=s-1;return r!==c&&(d=yt(r),d=r<0?ce(s+d,0):he(d,s-1)),Br(l,ct(i,3),d,!0)}function mn(l){var i=l==null?0:l.length;return i?Dt(l,1):[]}function Yh(l){var i=l==null?0:l.length;return i?Dt(l,$):[]}function ai(l,i){var r=l==null?0:l.length;return r?(i=i===c?1:yt(i),Dt(l,i)):[]}function Vi(l){for(var i=-1,r=l==null?0:l.length,s={};++i<r;){var d=l[i];s[d[0]]=d[1]}return s}function Bl(l){return l&&l.length?l[0]:c}function Eo(l,i,r){var s=l==null?0:l.length;if(!s)return-1;var d=r==null?0:yt(r);return d<0&&(d=ce(s+d,0)),du(l,i,d)}function Mo(l){var i=l==null?0:l.length;return i?wn(l,0,-1):[]}var pl=_t(function(l){var i=se(l,df);return i.length&&i[0]===l[0]?Pc(i):[]}),Xh=_t(function(l){var i=Ke(l),r=se(l,df);return i===Ke(r)?i=c:r.pop(),r.length&&r[0]===l[0]?Pc(r,ct(i,2)):[]}),Bu=_t(function(l){var i=Ke(l),r=se(l,df);return i=typeof i=="function"?i:c,i&&r.pop(),r.length&&r[0]===l[0]?Pc(r,c,i):[]});function Ia(l,i){return l==null?"":Hn.call(l,i)}function Ke(l){var i=l==null?0:l.length;return i?l[i-1]:c}function Lu(l,i,r){var s=l==null?0:l.length;if(!s)return-1;var d=s;return r!==c&&(d=yt(r),d=d<0?ce(s+d,0):he(d,s-1)),i===i?xn(l,i,d):Br(l,gu,d,!0)}function Qh(l,i){return l&&l.length?ao(l,yt(i)):c}var Zh=_t(Hu);function Hu(l,i){return l&&l.length&&i&&i.length?ff(l,i):l}function Vh(l,i,r){return l&&l.length&&i&&i.length?ff(l,i,ct(r,2)):l}function Mf(l,i,r){return l&&l.length&&i&&i.length?ff(l,i,c,r):l}var Kh=oe(function(l,i){var r=l==null?0:l.length,s=ka(l,i);return fo(l,se(i,function(d){return Pn(d,r)?+d:d}).sort(Na)),s});function Tg(l,i){var r=[];if(!(l&&l.length))return r;var s=-1,d=[],b=l.length;for(i=ct(i,3);++s<b;){var _=l[s];i(_,s,l)&&(r.push(_),d.push(s))}return fo(l,d),r}function Do(l){return l==null?l:Vc.call(l)}function Jt(l,i,r){var s=l==null?0:l.length;return s?(r&&typeof r!="number"&&Pe(l,i,r)?(i=0,r=s):(i=i==null?0:yt(i),r=r===c?s:yt(r)),wn(l,i,r)):[]}function re(l,i){return Ra(l,i)}function kt(l,i,r){return sf(l,i,ct(r,2))}function Bt(l,i){var r=l==null?0:l.length;if(r){var s=Ra(l,i);if(s<r&&vn(l[s],i))return s}return-1}function Wt(l,i){return Ra(l,i,!0)}function En(l,i,r){return sf(l,i,ct(r,2),!0)}function ri(l,i){var r=l==null?0:l.length;if(r){var s=Ra(l,i,!0)-1;if(vn(l[s],i))return s}return-1}function Nu(l){return l&&l.length?oo(l):[]}function Co(l,i){return l&&l.length?oo(l,ct(i,2)):[]}function Ll(l){var i=l==null?0:l.length;return i?wn(l,1,i):[]}function ye(l,i,r){return l&&l.length?(i=r||i===c?1:yt(i),wn(l,0,i<0?0:i)):[]}function fi(l,i,r){var s=l==null?0:l.length;return s?(i=r||i===c?1:yt(i),i=s-i,wn(l,i<0?0:i,s)):[]}function Ki(l,i){return l&&l.length?Ba(l,ct(i,3),!1,!0):[]}function Oo(l,i){return l&&l.length?Ba(l,ct(i,3)):[]}var Zn=_t(function(l){return El(Dt(l,1,Se,!0))}),qu=_t(function(l){var i=Ke(l);return Se(i)&&(i=c),El(Dt(l,1,Se,!0),ct(i,2))}),Pa=_t(function(l){var i=Ke(l);return i=typeof i=="function"?i:c,El(Dt(l,1,Se,!0),c,i)});function pn(l){return l&&l.length?El(l):[]}function Ro(l,i){return l&&l.length?El(l,ct(i,2)):[]}function Uo(l,i){return i=typeof i=="function"?i:c,l&&l.length?El(l,c,i):[]}function Gu(l){if(!(l&&l.length))return[];var i=0;return l=fl(l,function(r){if(Se(r))return i=ce(r.length,i),!0}),Kl(i,function(r){return se(l,Lr(r))})}function tr(l,i){if(!(l&&l.length))return[];var r=Gu(l);return i==null?r:se(r,function(s){return an(i,c,s)})}var ci=_t(function(l,i){return Se(l)?ti(l,i):[]}),Qe=_t(function(l){return La(fl(l,Se))}),oi=_t(function(l){var i=Ke(l);return Se(i)&&(i=c),La(fl(l,Se),ct(i,2))}),Yu=_t(function(l){var i=Ke(l);return i=typeof i=="function"?i:c,La(fl(l,Se),c,i)}),Xu=_t(Gu);function Bo(l,i){return ho(l||[],i||[],Ui)}function Lo(l,i){return ho(l||[],i||[],Du)}var jh=_t(function(l){var i=l.length,r=i>1?l[i-1]:c;return r=typeof r=="function"?(l.pop(),r):c,tr(l,r)});function Qu(l){var i=p(l);return i.__chain__=!0,i}function Ho(l,i){return i(l),l}function je(l,i){return i(l)}var Jh=oe(function(l){var i=l.length,r=i?l[0]:0,s=this.__wrapped__,d=function(b){return ka(b,l)};return i>1||this.__actions__.length||!(s instanceof At)||!Pn(r)?this.thru(d):(s=s.slice(r,+r+(i?1:0)),s.__actions__.push({func:je,args:[d],thisArg:c}),new cn(s,this.__chain__).thru(function(b){return i&&!b.length&&b.push(c),b}))});function Mn(){return Qu(this)}function Wh(){return new cn(this.value(),this.__chain__)}function Fh(){this.__values__===c&&(this.__values__=Dn(this.value()));var l=this.__index__>=this.__values__.length,i=l?c:this.__values__[this.__index__++];return{done:l,value:i}}function wg(){return this}function si(l){for(var i,r=this;r instanceof Gn;){var s=Ee(r);s.__index__=0,s.__values__=c,i?d.__wrapped__=s:i=s;var d=s;r=r.__wrapped__}return d.__wrapped__=l,i}function Df(){var l=this.__wrapped__;if(l instanceof At){var i=l;return this.__actions__.length&&(i=new At(this)),i=i.reverse(),i.__actions__.push({func:je,args:[Do],thisArg:c}),new cn(i,this.__chain__)}return this.thru(Do)}function No(){return ph(this.__wrapped__,this.__actions__)}var Zu=Gi(function(l,i,r){Yt.call(l,r)?++l[r]:Fn(l,r,1)});function $h(l,i,r){var s=vt(l)?Ls:$c;return r&&Pe(l,i,r)&&(i=c),s(l,ct(i,3))}function Ih(l,i){var r=vt(l)?fl:jt;return r(l,ct(i,3))}var Ph=Dl(Qn),qo=Dl(zo);function Go(l,i){return Dt(er(l,i),1)}function kg(l,i){return Dt(er(l,i),$)}function zg(l,i,r){return r=r===c?1:yt(r),Dt(er(l,i),r)}function td(l,i){var r=vt(l)?Un:zl;return r(l,ct(i,3))}function Yo(l,i){var r=vt(l)?ug:Pr;return r(l,ct(i,3))}var ed=Gi(function(l,i,r){Yt.call(l,r)?l[r].push(i):Fn(l,r,[i])});function Vu(l,i,r,s){l=Me(l)?l:Iu(l),r=r&&!s?yt(r):0;var d=l.length;return r<0&&(r=ce(d+r,0)),Xf(l)?r<=d&&l.indexOf(i,r)>-1:!!d&&du(l,i,r)>-1}var Cf=_t(function(l,i,r){var s=-1,d=typeof i=="function",b=Me(l)?H(l.length):[];return zl(l,function(_){b[++s]=d?an(i,_,r):Hi(_,i,r)}),b}),nd=Gi(function(l,i,r){Fn(l,r,i)});function er(l,i){var r=vt(l)?se:io;return r(l,ct(i,3))}function ld(l,i,r,s){return l==null?[]:(vt(i)||(i=i==null?[]:[i]),r=s?c:r,vt(r)||(r=r==null?[]:[r]),ro(l,i,r))}var id=Gi(function(l,i,r){l[r?0:1].push(i)},function(){return[[],[]]});function ud(l,i,r){var s=vt(l)?Uc:Hc,d=arguments.length<3;return s(l,ct(i,4),r,d,zl)}function ad(l,i,r){var s=vt(l)?ag:Hc,d=arguments.length<3;return s(l,ct(i,4),r,d,Pr)}function Of(l,i){var r=vt(l)?fl:jt;return r(l,ar(ct(i,3)))}function rd(l){var i=vt(l)?wu:Mu;return i(l)}function fd(l,i,r){(r?Pe(l,i,r):i===c)?i=1:i=yt(i);var s=vt(l)?ih:of;return s(l,i)}function ie(l){var i=vt(l)?uh:gh;return i(l)}function Xo(l){if(l==null)return 0;if(Me(l))return Xf(l)?cl(l):l.length;var i=qe(l);return i==bn||i==nn?l.size:lf(l).length}function Eg(l,i,r){var s=vt(l)?Bc:mh;return r&&Pe(l,i,r)&&(i=c),s(l,ct(i,3))}var cd=_t(function(l,i){if(l==null)return[];var r=i.length;return r>1&&Pe(l,i[0],i[1])?i=[]:r>2&&Pe(i[0],i[1],i[2])&&(i=[i[0]]),ro(l,Dt(i,1),[])}),Rf=Mi||function(){return _e.Date.now()};function Mg(l,i){if(typeof i!="function")throw new An(y);return l=yt(l),function(){if(--l<1)return i.apply(this,arguments)}}function od(l,i,r){return i=r?c:i,i=l&&i==null?l.length:i,dl(l,$t,c,c,c,c,i)}function nr(l,i){var r;if(typeof i!="function")throw new An(y);return l=yt(l),function(){return--l>0&&(r=i.apply(this,arguments)),l<=1&&(i=c),r}}var hi=_t(function(l,i,r){var s=et;if(r.length){var d=Jl(r,Zi(hi));s|=Ct}return dl(l,s,i,r,d)}),Uf=_t(function(l,i,r){var s=et|ut;if(r.length){var d=Jl(r,Zi(Uf));s|=Ct}return dl(i,s,l,r,d)});function lr(l,i,r){i=r?c:i;var s=dl(l,it,c,c,c,c,c,i);return s.placeholder=lr.placeholder,s}function Bf(l,i,r){i=r?c:i;var s=dl(l,Ut,c,c,c,c,c,i);return s.placeholder=Bf.placeholder,s}function di(l,i,r){var s,d,b,_,k,O,V=0,J=!1,W=!1,tt=!0;if(typeof l!="function")throw new An(y);i=Kn(i)||0,me(r)&&(J=!!r.leading,W="maxWait"in r,b=W?ce(Kn(r.maxWait)||0,i):b,tt="trailing"in r?!!r.trailing:tt);function at(Oe){var ql=s,Ji=d;return s=d=c,V=Oe,_=l.apply(Ji,ql),_}function mt(Oe){return V=Oe,k=Uu(Rt,i),J?at(Oe):_}function wt(Oe){var ql=Oe-O,Ji=Oe-V,A0=i-ql;return W?he(A0,b-Ji):A0}function pt(Oe){var ql=Oe-O,Ji=Oe-V;return O===c||ql>=i||ql<0||W&&Ji>=b}function Rt(){var Oe=Rf();if(pt(Oe))return Gt(Oe);k=Uu(Rt,wt(Oe))}function Gt(Oe){return k=c,tt&&s?at(Oe):(s=d=c,_)}function nl(){k!==c&&bh(k),V=0,s=O=d=k=c}function Cn(){return k===c?_:Gt(Rf())}function ll(){var Oe=Rf(),ql=pt(Oe);if(s=arguments,d=this,O=Oe,ql){if(k===c)return mt(O);if(W)return bh(k),k=Uu(Rt,i),at(O)}return k===c&&(k=Uu(Rt,i)),_}return ll.cancel=nl,ll.flush=Cn,ll}var vl=_t(function(l,i){return Bi(l,1,i)}),ir=_t(function(l,i,r){return Bi(l,Kn(i)||0,r)});function Dg(l){return dl(l,ue)}function ur(l,i){if(typeof l!="function"||i!=null&&typeof i!="function")throw new An(y);var r=function(){var s=arguments,d=i?i.apply(this,s):s[0],b=r.cache;if(b.has(d))return b.get(d);var _=l.apply(this,s);return r.cache=b.set(d,_)||b,_};return r.cache=new(ur.Cache||on),r}ur.Cache=on;function ar(l){if(typeof l!="function")throw new An(y);return function(){var i=arguments;switch(i.length){case 0:return!l.call(this);case 1:return!l.call(this,i[0]);case 2:return!l.call(this,i[0],i[1]);case 3:return!l.call(this,i[0],i[1],i[2])}return!l.apply(this,i)}}function sd(l){return nr(2,l)}var hd=vh(function(l,i){i=i.length==1&&vt(i[0])?se(i[0],_n(ct())):se(Dt(i,1),_n(ct()));var r=i.length;return _t(function(s){for(var d=-1,b=he(s.length,r);++d<b;)s[d]=i[d].call(this,s[d]);return an(l,this,s)})}),Qo=_t(function(l,i){var r=Jl(i,Zi(Qo));return dl(l,Ct,c,i,r)}),Lf=_t(function(l,i){var r=Jl(i,Zi(Lf));return dl(l,Lt,c,i,r)}),dd=oe(function(l,i){return dl(l,St,c,c,c,i)});function gd(l,i){if(typeof l!="function")throw new An(y);return i=i===c?i:yt(i),_t(l,i)}function Cg(l,i){if(typeof l!="function")throw new An(y);return i=i==null?0:ce(yt(i),0),_t(function(r){var s=r[i],d=ni(r,0,i);return s&&xl(d,s),an(l,this,d)})}function Zo(l,i,r){var s=!0,d=!0;if(typeof l!="function")throw new An(y);return me(r)&&(s="leading"in r?!!r.leading:s,d="trailing"in r?!!r.trailing:d),di(l,i,{leading:s,maxWait:i,trailing:d})}function Vo(l){return od(l,1)}function Og(l,i){return Qo(Ha(i),l)}function Rg(){if(!arguments.length)return[];var l=arguments[0];return vt(l)?l:[l]}function rr(l){return Ze(l,F)}function Ug(l,i){return i=typeof i=="function"?i:c,Ze(l,F,i)}function md(l){return Ze(l,M|F)}function zt(l,i){return i=typeof i=="function"?i:c,Ze(l,M|F,i)}function Ko(l,i){return i==null||Fc(l,i,Ce(i))}function vn(l,i){return l===i||l!==l&&i!==i}var jo=Qi(za),pd=Qi(function(l,i){return l>=i}),Hl=tf(function(){return arguments}())?tf:function(l){return pe(l)&&Yt.call(l,"callee")&&!zi.call(l,"callee")},vt=H.isArray,Hf=Rr?_n(Rr):sn;function Me(l){return l!=null&&ji(l.length)&&!tl(l)}function Se(l){return pe(l)&&Me(l)}function Bg(l){return l===!0||l===!1||pe(l)&&Ot(l)==Gl}var gi=jr||u0,vd=Cs?_n(Cs):Ae;function bd(l){return pe(l)&&l.nodeType===1&&!Vn(l)}function Nf(l){if(l==null)return!0;if(Me(l)&&(vt(l)||typeof l=="string"||typeof l.splice=="function"||gi(l)||Ku(l)||Hl(l)))return!l.length;var i=qe(l);if(i==bn||i==nn)return!l.size;if(le(l))return!lf(l).length;for(var r in l)if(Yt.call(l,r))return!1;return!0}function te(l,i){return Ni(l,i)}function Jo(l,i,r){r=typeof r=="function"?r:c;var s=r?r(l,i):c;return s===c?Ni(l,i,c,r):!!s}function De(l){if(!pe(l))return!1;var i=Ot(l);return i==Si||i==fc||typeof l.message=="string"&&typeof l.name=="string"&&!Vn(l)}function Lg(l){return typeof l=="number"&&ba(l)}function tl(l){if(!me(l))return!1;var i=Ot(l);return i==Fi||i==yr||i==br||i==gs}function qf(l){return typeof l=="number"&&l==yt(l)}function ji(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=ft}function me(l){var i=typeof l;return l!=null&&(i=="object"||i=="function")}function pe(l){return l!=null&&typeof l=="object"}var Gf=Os?_n(Os):ef;function Wo(l,i){return l===i||Ma(l,i,_f(i))}function Hg(l,i,r){return r=typeof r=="function"?r:c,Ma(l,i,_f(i),r)}function yd(l){return _d(l)&&l!=+l}function Ng(l){if(ja(l))throw new gt(T);return sl(l)}function Sd(l){return l===null}function qg(l){return l==null}function _d(l){return typeof l=="number"||pe(l)&&Ot(l)==ta}function Vn(l){if(!pe(l)||Ot(l)!=Be)return!1;var i=ma(l);if(i===null)return!0;var r=Yt.call(i,"constructor")&&i.constructor;return typeof r=="function"&&r instanceof r&&ga.call(r)==Ln}var Yf=Rs?_n(Rs):nf;function Fo(l){return qf(l)&&l>=-ft&&l<=ft}var xd=Us?_n(Us):eo;function Xf(l){return typeof l=="string"||!vt(l)&&pe(l)&&Ot(l)==ea}function tn(l){return typeof l=="symbol"||pe(l)&&Ot(l)==na}var Ku=Bs?_n(Bs):no;function el(l){return l===c}function $o(l){return pe(l)&&qe(l)==la}function Ad(l){return pe(l)&&Ot(l)==ia}var Td=Qi(uf),fr=Qi(function(l,i){return l<=i});function Dn(l){if(!l)return[];if(Me(l))return Xf(l)?Bn(l):ze(l);if(Ei&&l[Ei])return Zs(l[Ei]());var i=qe(l),r=i==bn?Yr:i==nn?Xr:Iu;return r(l)}function bl(l){if(!l)return l===0?l:0;if(l=Kn(l),l===$||l===-$){var i=l<0?-1:1;return i*It}return l===l?l:0}function yt(l){var i=bl(l),r=i%1;return i===i?r?i-r:i:0}function yl(l){return l?$n(yt(l),0,ae):0}function Kn(l){if(typeof l=="number")return l;if(tn(l))return bt;if(me(l)){var i=typeof l.valueOf=="function"?l.valueOf():l;l=me(i)?i+"":i}if(typeof l!="string")return l===0?l:+l;l=Gs(l);var r=He.test(l);return r||xs.test(l)?lg(l.slice(2),r?2:8):uu.test(l)?bt:+l}function wd(l){return dn(l,Ge(l))}function ju(l){return l?$n(yt(l),-ft,ft):l===0?l:0}function Xt(l){return l==null?"":hn(l)}var Gg=Yi(function(l,i){if(le(i)||Me(i)){dn(i,Ce(i),l);return}for(var r in i)Yt.call(i,r)&&Ui(l,r,i[r])}),kd=Yi(function(l,i){dn(i,Ge(i),l)}),Qf=Yi(function(l,i,r,s){dn(i,Ge(i),l,s)}),Yg=Yi(function(l,i,r,s){dn(i,Ce(i),l,s)}),Xg=oe(ka);function Qg(l,i){var r=Tn(l);return i==null?r:kl(r,i)}var Zg=_t(function(l,i){l=Nt(l);var r=-1,s=i.length,d=s>2?i[2]:c;for(d&&Pe(i[0],i[1],d)&&(s=1);++r<s;)for(var b=i[r],_=Ge(b),k=-1,O=_.length;++k<O;){var V=_[k],J=l[V];(J===c||vn(J,yu[V])&&!Yt.call(l,V))&&(l[V]=b[V])}return l}),zd=_t(function(l){return l.push(c,So),an(hr,c,l)});function Ju(l,i){return Hs(l,ct(i,3),Ve)}function cr(l,i){return Hs(l,ct(i,3),Eu)}function Ed(l,i){return l==null?l:zu(l,ct(i,3),Ge)}function Vg(l,i){return l==null?l:Li(l,ct(i,3),Ge)}function Wu(l,i){return l&&Ve(l,ct(i,3))}function or(l,i){return l&&Eu(l,ct(i,3))}function Md(l){return l==null?[]:ei(l,Ce(l))}function Zf(l){return l==null?[]:ei(l,Ge(l))}function sr(l,i,r){var s=l==null?c:Yn(l,i);return s===c?r:s}function Io(l,i){return l!=null&&Ka(l,i,Ic)}function Fu(l,i){return l!=null&&Ka(l,i,fh)}var Dd=Ga(function(l,i,r){i!=null&&typeof i.toString!="function"&&(i=We.call(i)),l[i]=r},U(B)),Cd=Ga(function(l,i,r){i!=null&&typeof i.toString!="function"&&(i=We.call(i)),Yt.call(l,i)?l[i].push(r):l[i]=[r]},ct),Kg=_t(Hi);function Ce(l){return Me(l)?Wc(l):lf(l)}function Ge(l){return Me(l)?Wc(l,!0):oh(l)}function jg(l,i){var r={};return i=ct(i,3),Ve(l,function(s,d,b){Fn(r,i(s,d,b),s)}),r}function Jg(l,i){var r={};return i=ct(i,3),Ve(l,function(s,d,b){Fn(r,d,i(s,d,b))}),r}var Wg=Yi(function(l,i,r){Da(l,i,r)}),hr=Yi(function(l,i,r,s){Da(l,i,r,s)}),Vf=oe(function(l,i){var r={};if(l==null)return r;var s=!1;i=se(i,function(b){return b=Ml(b,l),s||(s=b.length>1),b}),dn(l,yf(l),r),s&&(r=Ze(r,M|N|F,kh));for(var d=i.length;d--;)Ua(r,i[d]);return r});function Po(l,i){return $u(l,ar(ct(i)))}var Fg=oe(function(l,i){return l==null?{}:sh(l,i)});function $u(l,i){if(l==null)return{};var r=se(yf(l),function(s){return[s]});return i=ct(i),rf(l,r,function(s,d){return i(s,d[0])})}function $g(l,i,r){i=Ml(i,l);var s=-1,d=i.length;for(d||(d=1,l=c);++s<d;){var b=l==null?c:l[Vt(i[s])];b===c&&(s=d,b=r),l=tl(b)?b.call(l):b}return l}function Od(l,i,r){return l==null?l:Du(l,i,r)}function Rd(l,i,r,s){return s=typeof s=="function"?s:c,l==null?l:Du(l,i,r,s)}var ts=Qa(Ce),es=Qa(Ge);function ns(l,i,r){var s=vt(l),d=s||gi(l)||Ku(l);if(i=ct(i,4),r==null){var b=l&&l.constructor;d?r=s?new b:[]:me(l)?r=tl(b)?Tn(ma(l)):{}:r={}}return(d?Un:Ve)(l,function(_,k,O){return i(r,_,k,O)}),r}function Ud(l,i){return l==null?!0:Ua(l,i)}function Kf(l,i,r){return l==null?l:so(l,i,Ha(r))}function Ig(l,i,r,s){return s=typeof s=="function"?s:c,l==null?l:so(l,i,Ha(r),s)}function Iu(l){return l==null?[]:qr(l,Ce(l))}function ls(l){return l==null?[]:qr(l,Ge(l))}function is(l,i,r){return r===c&&(r=i,i=c),r!==c&&(r=Kn(r),r=r===r?r:0),i!==c&&(i=Kn(i),i=i===i?i:0),$n(Kn(l),i,r)}function jf(l,i,r){return i=bl(i),r===c?(r=i,i=0):r=bl(r),l=Kn(l),ch(l,i,r)}function us(l,i,r){if(r&&typeof r!="boolean"&&Pe(l,i,r)&&(i=r=c),r===c&&(typeof i=="boolean"?(r=i,i=c):typeof l=="boolean"&&(r=l,l=c)),l===c&&i===c?(l=0,i=1):(l=bl(l),i===c?(i=l,l=0):i=bl(i)),l>i){var s=l;l=i,i=s}if(r||l%1||i%1){var d=Zc();return he(l+d*(i-l+ha("1e-"+((d+"").length-1))),i)}return cf(l,i)}var Bd=Ie(function(l,i,r){return i=i.toLowerCase(),l+(r?Jf(i):i)});function Jf(l){return u(Xt(l).toLowerCase())}function Nl(l){return l=Xt(l),l&&l.replace(au,Gr).replace(_l,"")}function mi(l,i,r){l=Xt(l),i=hn(i);var s=l.length;r=r===c?s:$n(yt(r),0,s);var d=r;return r-=i.length,r>=0&&l.slice(r,d)==i}function pi(l){return l=Xt(l),l&&vs.test(l)?l.replace(eu,Ys):l}function dr(l){return l=Xt(l),l&&ln.test(l)?l.replace(Le,"\\$&"):l}var gr=Ie(function(l,i,r){return l+(r?"-":"")+i.toLowerCase()}),vi=Ie(function(l,i,r){return l+(r?" ":"")+i.toLowerCase()}),Pg=kn("toLowerCase");function Ld(l,i,r){l=Xt(l),i=yt(i);var s=i?cl(l):0;if(!i||s>=i)return l;var d=(i-s)/2;return Xa(Al(d),r)+l+Xa(xu(d),r)}function mr(l,i,r){l=Xt(l),i=yt(i);var s=i?cl(l):0;return i&&s<i?l+Xa(i-s,r):l}function t0(l,i,r){l=Xt(l),i=yt(i);var s=i?cl(l):0;return i&&s<i?Xa(i-s,r)+l:l}function Hd(l,i,r){return r||i==null?i=0:i&&(i=+i),Di(Xt(l).replace(Xl,""),i||0)}function Wf(l,i,r){return(r?Pe(l,i,r):i===c)?i=1:i=yt(i),co(Xt(l),i)}function Nd(){var l=arguments,i=Xt(l[0]);return l.length<3?i:i.replace(l[1],l[2])}var e0=Ie(function(l,i,r){return l+(r?"_":"")+i.toLowerCase()});function Ff(l,i,r){return r&&typeof r!="number"&&Pe(l,i,r)&&(i=r=c),r=r===c?ae:r>>>0,r?(l=Xt(l),l&&(typeof i=="string"||i!=null&&!Yf(i))&&(i=hn(i),!i&&jl(l))?ni(Bn(l),0,r):l.split(i,r)):[]}var $f=Ie(function(l,i,r){return l+(r?" ":"")+u(i)});function qd(l,i,r){return l=Xt(l),r=r==null?0:$n(yt(r),0,l.length),i=hn(i),l.slice(r,r+i.length)==i}function pr(l,i,r){var s=p.templateSettings;r&&Pe(l,i,r)&&(i=c),l=Xt(l),i=Qf({},i,s,vf);var d=Qf({},i.imports,s.imports,vf),b=Ce(d),_=qr(d,b),k,O,V=0,J=i.interpolate||xr,W="__p += '",tt=Qr((i.escape||xr).source+"|"+J.source+"|"+(J===_r?lu:xr).source+"|"+(i.evaluate||xr).source+"|$","g"),at="//# sourceURL="+(Yt.call(i,"sourceURL")?(i.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Es+"]")+`
`;l.replace(tt,function(pt,Rt,Gt,nl,Cn,ll){return Gt||(Gt=nl),W+=l.slice(V,ll).replace(As,Xs),Rt&&(k=!0,W+=`' +
__e(`+Rt+`) +
'`),Cn&&(O=!0,W+=`';
`+Cn+`;
__p += '`),Gt&&(W+=`' +
((__t = (`+Gt+`)) == null ? '' : __t) +
'`),V=ll+pt.length,pt}),W+=`';
`;var mt=Yt.call(i,"variable")&&i.variable;if(!mt)W=`with (obj) {
`+W+`
}
`;else if(gc.test(mt))throw new gt(z);W=(O?W.replace(Fd,""):W).replace(ms,"$1").replace(ps,"$1;"),W="function("+(mt||"obj")+`) {
`+(mt?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(k?", __e = _.escape":"")+(O?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+W+`return __p
}`;var wt=f(function(){return Ht(b,at+"return "+W).apply(c,_)});if(wt.source=W,De(wt))throw wt;return wt}function as(l){return Xt(l).toLowerCase()}function If(l){return Xt(l).toUpperCase()}function Gd(l,i,r){if(l=Xt(l),l&&(r||i===c))return Gs(l);if(!l||!(i=hn(i)))return l;var s=Bn(l),d=Bn(i),b=pu(s,d),_=vu(s,d)+1;return ni(s,b,_).join("")}function n0(l,i,r){if(l=Xt(l),l&&(r||i===c))return l.slice(0,qc(l)+1);if(!l||!(i=hn(i)))return l;var s=Bn(l),d=vu(s,Bn(i))+1;return ni(s,0,d).join("")}function Pf(l,i,r){if(l=Xt(l),l&&(r||i===c))return l.replace(Xl,"");if(!l||!(i=hn(i)))return l;var s=Bn(l),d=pu(s,Bn(i));return ni(s,d).join("")}function t(l,i){var r=K,s=Re;if(me(i)){var d="separator"in i?i.separator:d;r="length"in i?yt(i.length):r,s="omission"in i?hn(i.omission):s}l=Xt(l);var b=l.length;if(jl(l)){var _=Bn(l);b=_.length}if(r>=b)return l;var k=r-cl(s);if(k<1)return s;var O=_?ni(_,0,k).join(""):l.slice(0,k);if(d===c)return O+s;if(_&&(k+=O.length-k),Yf(d)){if(l.slice(k).search(d)){var V,J=O;for(d.global||(d=Qr(d.source,Xt(iu.exec(d))+"g")),d.lastIndex=0;V=d.exec(J);)var W=V.index;O=O.slice(0,W===c?k:W)}}else if(l.indexOf(hn(d),k)!=k){var tt=O.lastIndexOf(d);tt>-1&&(O=O.slice(0,tt))}return O+s}function e(l){return l=Xt(l),l&&$d.test(l)?l.replace(Sr,Ks):l}var n=Ie(function(l,i,r){return l+(r?" ":"")+i.toUpperCase()}),u=kn("toUpperCase");function a(l,i,r){return l=Xt(l),i=r?c:i,i===c?Qs(l)?gg(l):cg(l):l.match(i)||[]}var f=_t(function(l,i){try{return an(l,c,i)}catch(r){return De(r)?r:new gt(r)}}),h=oe(function(l,i){return Un(i,function(r){r=Vt(r),Fn(l,r,hi(l[r],l))}),l});function v(l){var i=l==null?0:l.length,r=ct();return l=i?se(l,function(s){if(typeof s[1]!="function")throw new An(y);return[r(s[0]),s[1]]}):[],_t(function(s){for(var d=-1;++d<i;){var b=l[d];if(an(b[0],this,s))return an(b[1],this,s)}})}function A(l){return ah(Ze(l,M))}function U(l){return function(){return l}}function Y(l,i){return l==null||l!==l?i:l}var j=hl(),L=hl(!0);function B(l){return l}function st(l){return lo(typeof l=="function"?l:Ze(l,M))}function ot(l){return uo(Ze(l,M))}function ee(l,i){return af(l,Ze(i,M))}var C=_t(function(l,i){return function(r){return Hi(r,l,i)}}),E=_t(function(l,i){return function(r){return Hi(l,r,i)}});function R(l,i,r){var s=Ce(i),d=ei(i,s);r==null&&!(me(i)&&(d.length||!s.length))&&(r=i,i=l,l=this,d=ei(i,Ce(i)));var b=!(me(r)&&"chain"in r)||!!r.chain,_=tl(l);return Un(d,function(k){var O=i[k];l[k]=O,_&&(l.prototype[k]=function(){var V=this.__chain__;if(b||V){var J=l(this.__wrapped__),W=J.__actions__=ze(this.__actions__);return W.push({func:O,args:arguments,thisArg:l}),J.__chain__=V,J}return O.apply(l,xl([this.value()],arguments))})}),l}function Q(){return _e._===this&&(_e._=Su),this}function lt(){}function Tt(l){return l=yt(l),_t(function(i){return ao(i,l)})}var rt=Ya(se),ht=Ya(Ls),Ye=Ya(Bc);function qt(l){return Af(l)?Lr(Vt(l)):hh(l)}function bi(l){return function(i){return l==null?c:Yn(l,i)}}var l0=li(),p1=li(!0);function i0(){return[]}function u0(){return!1}function v1(){return{}}function b1(){return""}function y1(){return!0}function S1(l,i){if(l=yt(l),l<1||l>ft)return[];var r=ae,s=he(l,ae);i=ct(i),l-=ae;for(var d=Kl(s,i);++r<l;)i(r);return d}function _1(l){return vt(l)?se(l,Vt):tn(l)?[l]:ze(ml(Xt(l)))}function x1(l){var i=++jn;return Xt(l)+i}var A1=Cu(function(l,i){return l+i},0),T1=pf("ceil"),w1=Cu(function(l,i){return l/i},1),k1=pf("floor");function z1(l){return l&&l.length?In(l,B,za):c}function E1(l,i){return l&&l.length?In(l,ct(i,2),za):c}function M1(l){return Ns(l,B)}function D1(l,i){return Ns(l,ct(i,2))}function C1(l){return l&&l.length?In(l,B,uf):c}function O1(l,i){return l&&l.length?In(l,ct(i,2),uf):c}var R1=Cu(function(l,i){return l*i},1),U1=pf("round"),B1=Cu(function(l,i){return l-i},0);function L1(l){return l&&l.length?Nr(l,B):0}function H1(l,i){return l&&l.length?Nr(l,ct(i,2)):0}return p.after=Mg,p.ary=od,p.assign=Gg,p.assignIn=kd,p.assignInWith=Qf,p.assignWith=Yg,p.at=Xg,p.before=nr,p.bind=hi,p.bindAll=h,p.bindKey=Uf,p.castArray=Rg,p.chain=Qu,p.chunk=xg,p.compact=Hh,p.concat=Nh,p.cond=v,p.conforms=A,p.constant=U,p.countBy=Zu,p.create=Qg,p.curry=lr,p.curryRight=Bf,p.debounce=di,p.defaults=Zg,p.defaultsDeep=zd,p.defer=vl,p.delay=ir,p.difference=ge,p.differenceBy=gn,p.differenceWith=Ul,p.drop=qh,p.dropRight=Gh,p.dropRightWhile=Ag,p.dropWhile=ko,p.fill=zn,p.filter=Ih,p.flatMap=Go,p.flatMapDeep=kg,p.flatMapDepth=zg,p.flatten=mn,p.flattenDeep=Yh,p.flattenDepth=ai,p.flip=Dg,p.flow=j,p.flowRight=L,p.fromPairs=Vi,p.functions=Md,p.functionsIn=Zf,p.groupBy=ed,p.initial=Mo,p.intersection=pl,p.intersectionBy=Xh,p.intersectionWith=Bu,p.invert=Dd,p.invertBy=Cd,p.invokeMap=Cf,p.iteratee=st,p.keyBy=nd,p.keys=Ce,p.keysIn=Ge,p.map=er,p.mapKeys=jg,p.mapValues=Jg,p.matches=ot,p.matchesProperty=ee,p.memoize=ur,p.merge=Wg,p.mergeWith=hr,p.method=C,p.methodOf=E,p.mixin=R,p.negate=ar,p.nthArg=Tt,p.omit=Vf,p.omitBy=Po,p.once=sd,p.orderBy=ld,p.over=rt,p.overArgs=hd,p.overEvery=ht,p.overSome=Ye,p.partial=Qo,p.partialRight=Lf,p.partition=id,p.pick=Fg,p.pickBy=$u,p.property=qt,p.propertyOf=bi,p.pull=Zh,p.pullAll=Hu,p.pullAllBy=Vh,p.pullAllWith=Mf,p.pullAt=Kh,p.range=l0,p.rangeRight=p1,p.rearg=dd,p.reject=Of,p.remove=Tg,p.rest=gd,p.reverse=Do,p.sampleSize=fd,p.set=Od,p.setWith=Rd,p.shuffle=ie,p.slice=Jt,p.sortBy=cd,p.sortedUniq=Nu,p.sortedUniqBy=Co,p.split=Ff,p.spread=Cg,p.tail=Ll,p.take=ye,p.takeRight=fi,p.takeRightWhile=Ki,p.takeWhile=Oo,p.tap=Ho,p.throttle=Zo,p.thru=je,p.toArray=Dn,p.toPairs=ts,p.toPairsIn=es,p.toPath=_1,p.toPlainObject=wd,p.transform=ns,p.unary=Vo,p.union=Zn,p.unionBy=qu,p.unionWith=Pa,p.uniq=pn,p.uniqBy=Ro,p.uniqWith=Uo,p.unset=Ud,p.unzip=Gu,p.unzipWith=tr,p.update=Kf,p.updateWith=Ig,p.values=Iu,p.valuesIn=ls,p.without=ci,p.words=a,p.wrap=Og,p.xor=Qe,p.xorBy=oi,p.xorWith=Yu,p.zip=Xu,p.zipObject=Bo,p.zipObjectDeep=Lo,p.zipWith=jh,p.entries=ts,p.entriesIn=es,p.extend=kd,p.extendWith=Qf,R(p,p),p.add=A1,p.attempt=f,p.camelCase=Bd,p.capitalize=Jf,p.ceil=T1,p.clamp=is,p.clone=rr,p.cloneDeep=md,p.cloneDeepWith=zt,p.cloneWith=Ug,p.conformsTo=Ko,p.deburr=Nl,p.defaultTo=Y,p.divide=w1,p.endsWith=mi,p.eq=vn,p.escape=pi,p.escapeRegExp=dr,p.every=$h,p.find=Ph,p.findIndex=Qn,p.findKey=Ju,p.findLast=qo,p.findLastIndex=zo,p.findLastKey=cr,p.floor=k1,p.forEach=td,p.forEachRight=Yo,p.forIn=Ed,p.forInRight=Vg,p.forOwn=Wu,p.forOwnRight=or,p.get=sr,p.gt=jo,p.gte=pd,p.has=Io,p.hasIn=Fu,p.head=Bl,p.identity=B,p.includes=Vu,p.indexOf=Eo,p.inRange=jf,p.invoke=Kg,p.isArguments=Hl,p.isArray=vt,p.isArrayBuffer=Hf,p.isArrayLike=Me,p.isArrayLikeObject=Se,p.isBoolean=Bg,p.isBuffer=gi,p.isDate=vd,p.isElement=bd,p.isEmpty=Nf,p.isEqual=te,p.isEqualWith=Jo,p.isError=De,p.isFinite=Lg,p.isFunction=tl,p.isInteger=qf,p.isLength=ji,p.isMap=Gf,p.isMatch=Wo,p.isMatchWith=Hg,p.isNaN=yd,p.isNative=Ng,p.isNil=qg,p.isNull=Sd,p.isNumber=_d,p.isObject=me,p.isObjectLike=pe,p.isPlainObject=Vn,p.isRegExp=Yf,p.isSafeInteger=Fo,p.isSet=xd,p.isString=Xf,p.isSymbol=tn,p.isTypedArray=Ku,p.isUndefined=el,p.isWeakMap=$o,p.isWeakSet=Ad,p.join=Ia,p.kebabCase=gr,p.last=Ke,p.lastIndexOf=Lu,p.lowerCase=vi,p.lowerFirst=Pg,p.lt=Td,p.lte=fr,p.max=z1,p.maxBy=E1,p.mean=M1,p.meanBy=D1,p.min=C1,p.minBy=O1,p.stubArray=i0,p.stubFalse=u0,p.stubObject=v1,p.stubString=b1,p.stubTrue=y1,p.multiply=R1,p.nth=Qh,p.noConflict=Q,p.noop=lt,p.now=Rf,p.pad=Ld,p.padEnd=mr,p.padStart=t0,p.parseInt=Hd,p.random=us,p.reduce=ud,p.reduceRight=ad,p.repeat=Wf,p.replace=Nd,p.result=$g,p.round=U1,p.runInContext=D,p.sample=rd,p.size=Xo,p.snakeCase=e0,p.some=Eg,p.sortedIndex=re,p.sortedIndexBy=kt,p.sortedIndexOf=Bt,p.sortedLastIndex=Wt,p.sortedLastIndexBy=En,p.sortedLastIndexOf=ri,p.startCase=$f,p.startsWith=qd,p.subtract=B1,p.sum=L1,p.sumBy=H1,p.template=pr,p.times=S1,p.toFinite=bl,p.toInteger=yt,p.toLength=yl,p.toLower=as,p.toNumber=Kn,p.toSafeInteger=ju,p.toString=Xt,p.toUpper=If,p.trim=Gd,p.trimEnd=n0,p.trimStart=Pf,p.truncate=t,p.unescape=e,p.uniqueId=x1,p.upperCase=n,p.upperFirst=u,p.each=td,p.eachRight=Yo,p.first=Bl,R(p,function(){var l={};return Ve(p,function(i,r){Yt.call(p.prototype,r)||(l[r]=i)}),l}(),{chain:!1}),p.VERSION=g,Un(["bind","bindKey","curry","curryRight","partial","partialRight"],function(l){p[l].placeholder=p}),Un(["drop","take"],function(l,i){At.prototype[l]=function(r){r=r===c?1:ce(yt(r),0);var s=this.__filtered__&&!i?new At(this):this.clone();return s.__filtered__?s.__takeCount__=he(r,s.__takeCount__):s.__views__.push({size:he(r,ae),type:l+(s.__dir__<0?"Right":"")}),s},At.prototype[l+"Right"]=function(r){return this.reverse()[l](r).reverse()}}),Un(["filter","map","takeWhile"],function(l,i){var r=i+1,s=r==en||r==X;At.prototype[l]=function(d){var b=this.clone();return b.__iteratees__.push({iteratee:ct(d,3),type:r}),b.__filtered__=b.__filtered__||s,b}}),Un(["head","last"],function(l,i){var r="take"+(i?"Right":"");At.prototype[l]=function(){return this[r](1).value()[0]}}),Un(["initial","tail"],function(l,i){var r="drop"+(i?"":"Right");At.prototype[l]=function(){return this.__filtered__?new At(this):this[r](1)}}),At.prototype.compact=function(){return this.filter(B)},At.prototype.find=function(l){return this.filter(l).head()},At.prototype.findLast=function(l){return this.reverse().find(l)},At.prototype.invokeMap=_t(function(l,i){return typeof l=="function"?new At(this):this.map(function(r){return Hi(r,l,i)})}),At.prototype.reject=function(l){return this.filter(ar(ct(l)))},At.prototype.slice=function(l,i){l=yt(l);var r=this;return r.__filtered__&&(l>0||i<0)?new At(r):(l<0?r=r.takeRight(-l):l&&(r=r.drop(l)),i!==c&&(i=yt(i),r=i<0?r.dropRight(-i):r.take(i-l)),r)},At.prototype.takeRightWhile=function(l){return this.reverse().takeWhile(l).reverse()},At.prototype.toArray=function(){return this.take(ae)},Ve(At.prototype,function(l,i){var r=/^(?:filter|find|map|reject)|While$/.test(i),s=/^(?:head|last)$/.test(i),d=p[s?"take"+(i=="last"?"Right":""):i],b=s||/^find/.test(i);d&&(p.prototype[i]=function(){var _=this.__wrapped__,k=s?[1]:arguments,O=_ instanceof At,V=k[0],J=O||vt(_),W=function(Rt){var Gt=d.apply(p,xl([Rt],k));return s&&tt?Gt[0]:Gt};J&&r&&typeof V=="function"&&V.length!=1&&(O=J=!1);var tt=this.__chain__,at=!!this.__actions__.length,mt=b&&!tt,wt=O&&!at;if(!b&&J){_=wt?_:new At(this);var pt=l.apply(_,k);return pt.__actions__.push({func:je,args:[W],thisArg:c}),new cn(pt,tt)}return mt&&wt?l.apply(this,k):(pt=this.thru(W),mt?s?pt.value()[0]:pt.value():pt)})}),Un(["pop","push","shift","sort","splice","unshift"],function(l){var i=Zr[l],r=/^(?:push|sort|unshift)$/.test(l)?"tap":"thru",s=/^(?:pop|shift)$/.test(l);p.prototype[l]=function(){var d=arguments;if(s&&!this.__chain__){var b=this.value();return i.apply(vt(b)?b:[],d)}return this[r](function(_){return i.apply(vt(_)?_:[],d)})}}),Ve(At.prototype,function(l,i){var r=p[i];if(r){var s=r.name+"";Yt.call(qn,s)||(qn[s]=[]),qn[s].push({name:i,func:r})}}),qn[be(c,ut).name]=[{name:"wrapper",func:c}],At.prototype.clone=xa,At.prototype.reverse=Jr,At.prototype.value=Oi,p.prototype.at=Jh,p.prototype.chain=Mn,p.prototype.commit=Wh,p.prototype.next=Fh,p.prototype.plant=si,p.prototype.reverse=Df,p.prototype.toJSON=p.prototype.valueOf=p.prototype.value=No,p.prototype.first=p.prototype.head,Ei&&(p.prototype[Ei]=wg),p},Wl=bu();Vl?((Vl.exports=Wl)._=Wl,hu._=Wl):_e._=Wl}).call(zv)}(cs,cs.exports)),cs.exports}var Mv=Ev();function Dv(){const{colorScheme:o}=v0(),[m,c]=Ft.useState({front:"",back:"",extra:"",contentVersion:0}),[g,S]=Ft.useState({front:"",back:"",extra:"",contentVersion:0}),[T,y]=Ft.useState([]),[z,x]=Ft.useState(null),[w,q]=Ft.useState(!1),[M,N]=Ft.useState(0);Ft.useEffect(()=>{Math.random()<=.002&&(q(!0),setTimeout(()=>{q(!1)},2e4))},[M]),Ft.useEffect(()=>{const it=()=>{const St=document.getElementById("fields")||document.querySelector(".fields");if(!St)return;const ue=Array.from(St.children).map(Et=>{const Qt=Et.querySelector("span.label-name"),en=Et.querySelector('[class*="rich-text-editable"]')?.shadowRoot?.querySelector("anki-editable")||Et.querySelector('[class*="rich-text-editable"]')?.querySelector("anki-editable");return!Qt||!en?null:{label:Qt.innerText?.trim(),content:en||""}}).filter(Et=>Et!==null);if(ue.some(Et=>Et.label.toLowerCase().includes("front")))c({front:ue.find(Et=>Et.label.toLowerCase().includes("front"))?.content||"",back:ue.find(Et=>Et.label.toLowerCase().includes("back"))?.content||"",extra:ue.find(Et=>Et.label.toLowerCase().includes("extra"))?.content||"",contentVersion:Date.now()}),S({front:"",back:"",extra:"",contentVersion:0});else{const Et={front:ue.find(Qt=>Qt.label.toLowerCase().includes("text"))?.content||"",extra:ue.find(Qt=>Qt.label.toLowerCase().includes("extra"))?.content||"",contentVersion:Date.now()};S({...Et}),c({front:"",back:"",extra:""})}const Re=ue.find(Et=>Et.label.toLowerCase().includes("difficulty"));x(Re?Re.content.textContent.trim():""),N(Et=>Et+1)},Ut=Mv.debounce(it,500);Ut();const Ct=new MutationObserver(St=>{Ut()}),Lt=document.body.querySelector(".note-editor");Ct.observe(Lt,{childList:!0,subtree:!0,characterData:!0,attributes:!0});const $t=St=>{Ut()};return window.addEventListener("keyup",$t),()=>{Ct.disconnect(),window.removeEventListener("keyup",$t)}},[]);const[F,Z]=Ft.useState(!1),[I,et]=Ft.useState(!1);Ft.useEffect(()=>{const it=m.front||m.back||m.extra,Ut=g.front||g.back||g.extra;Z(it),et(Ut)},[m,g]);const dt=(()=>{const it=o==="dark";return{front:{bg:it?"dark.6":"gray.1",border:it?"dark.4":"gray.3"},back:{bg:it?"dark.5":"blue.0",border:it?"blue.8":"blue.3"},extra:{bg:it?"dark.4":"yellow.1",border:it?"yellow.8":"yellow.3"}}})();return nt.jsxs(N0,{shadow:"sm",padding:"md",radius:"md",withBorder:!0,children:[nt.jsx(g1,{tags:T,difficulty:z}),F&&nt.jsx(h1,{contentVersion:m.contentVersion,colors:dt,frontNode:m.front,backNode:m.back,extraNode:m.extra}),I&&nt.jsx(d1,{contentVersion:g.contentVersion,colors:dt,frontNode:g.front,backNode:g.back,extraNode:g.extra})]})}const Cv=["front-card-basic","back-card-basic","extra-card-basic"],Ov=["front-card-cloze","back-card-cloze","extra-card-cloze"],m1=[...Cv,...Ov].some(o=>document.getElementById(o)!==null),g0=document.getElementById("root-react");g0&&g0.parentNode.removeChild(g0);const p0=document.createElement("div");p0.id="root-react";if(m1)document.body.appendChild(p0);else{const o=document.getElementById("fields")||document.querySelector(".fields");o?.parentNode?.insertBefore(p0,o.nextSibling)}const Rv=document.querySelector(".nightMode")||document.querySelector(".night-mode"),Uv=Rv?"dark":"light";rm.createRoot(document.getElementById("root-react")).render(nt.jsx(Ft.StrictMode,{children:nt.jsx(G1,{withGlobalClasses:!0,defaultColorScheme:Uv,children:m1?nt.jsx(kv,{}):nt.jsx(Dv,{})})}));
